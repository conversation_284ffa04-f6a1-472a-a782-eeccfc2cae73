// Typing test content organized by difficulty and test type

export const testContent = {
  easy: {
    basic: [
      "The quick brown fox jumps over the lazy dog. This is a simple sentence that contains all letters of the alphabet.",
      "I love to read books in my free time. Reading helps me learn new things and expand my vocabulary.",
      "The sun is shining bright today. It is a perfect day to go outside and enjoy the beautiful weather.",
      "My favorite color is blue because it reminds me of the ocean and the clear sky above.",
      "Cats are wonderful pets that bring joy and comfort to many families around the world."
    ],
    intermediate: [
      "Technology has revolutionized the way we communicate with each other. Social media platforms allow us to connect instantly with friends and family across the globe.",
      "Learning a new language opens doors to different cultures and opportunities. It requires dedication, practice, and patience to become fluent.",
      "Environmental conservation is crucial for maintaining the balance of our ecosystem. We must take action to protect our planet for future generations.",
      "The art of cooking combines creativity with science. Understanding ingredients and techniques helps create delicious and nutritious meals.",
      "Exercise and proper nutrition are essential components of a healthy lifestyle. Regular physical activity strengthens both body and mind."
    ],
    advanced: [
      "Artificial intelligence and machine learning algorithms are transforming industries by automating complex processes and providing unprecedented insights into data patterns.",
      "Quantum computing represents a paradigm shift in computational capabilities, potentially solving problems that are intractable for classical computers.",
      "The intersection of biotechnology and nanotechnology promises revolutionary advances in medicine, from targeted drug delivery to regenerative therapies.",
      "Cryptocurrency and blockchain technology challenge traditional financial systems by offering decentralized, transparent, and secure transaction mechanisms.",
      "Climate change mitigation strategies require interdisciplinary collaboration between scientists, policymakers, and communities to implement sustainable solutions."
    ]
  },
  medium: {
    basic: [
      "Programming languages like JavaScript, Python, and Java each have their own syntax and use cases. Developers choose languages based on project requirements.",
      "The process of photosynthesis converts sunlight into energy that plants use to grow. This fundamental biological process sustains most life on Earth.",
      "Modern smartphones contain powerful processors, high-resolution cameras, and sophisticated sensors that enable countless applications and features.",
      "Online education platforms have democratized access to knowledge, allowing students worldwide to learn from expert instructors and prestigious institutions.",
      "Renewable energy sources such as solar, wind, and hydroelectric power offer sustainable alternatives to fossil fuels for electricity generation."
    ],
    intermediate: [
      "Data visualization techniques transform complex datasets into comprehensible charts, graphs, and interactive displays that reveal hidden patterns and trends.",
      "Cybersecurity professionals employ various strategies including encryption, firewalls, and intrusion detection systems to protect digital assets from malicious attacks.",
      "User experience design focuses on creating intuitive, accessible, and enjoyable interactions between humans and digital products through research and testing.",
      "Supply chain management involves coordinating the flow of goods, information, and finances from suppliers to manufacturers to consumers efficiently.",
      "Genetic engineering techniques like CRISPR-Cas9 enable precise modifications to DNA sequences, opening possibilities for treating genetic disorders."
    ],
    advanced: [
      "Neuroplasticity research demonstrates the brain's remarkable ability to reorganize neural pathways throughout life, challenging previous assumptions about cognitive development.",
      "Distributed computing architectures leverage multiple interconnected systems to process large-scale computations more efficiently than single-machine approaches.",
      "Behavioral economics integrates psychological insights with economic theory to explain seemingly irrational decision-making patterns in markets and organizations.",
      "Metamaterials exhibit properties not found in naturally occurring substances, enabling applications like invisibility cloaking and super-resolution imaging.",
      "Epigenetic mechanisms regulate gene expression without altering DNA sequences, influencing traits that can be inherited across multiple generations."
    ]
  },
  hard: {
    basic: [
      "The implementation of microservices architecture requires careful consideration of service boundaries, communication protocols, and data consistency patterns.",
      "Quantum entanglement phenomena demonstrate non-local correlations between particles that Einstein famously described as 'spooky action at a distance.'",
      "Advanced cryptographic protocols utilize mathematical complexity to ensure confidentiality, integrity, and authenticity in digital communications.",
      "Bioinformatics algorithms process genomic sequences to identify patterns, predict protein structures, and understand evolutionary relationships.",
      "Machine learning models require extensive training datasets, feature engineering, and hyperparameter tuning to achieve optimal performance."
    ],
    intermediate: [
      "Computational fluid dynamics simulations model complex flow phenomena using numerical methods to solve partial differential equations governing fluid motion.",
      "Reinforcement learning agents optimize decision-making strategies through trial-and-error interactions with dynamic environments and reward signals.",
      "Distributed consensus algorithms like Raft and PBFT ensure consistency across replicated systems despite network partitions and Byzantine failures.",
      "Spectroscopic analysis techniques identify molecular compositions by measuring electromagnetic radiation absorption and emission characteristics.",
      "Topological quantum computing exploits anyonic braiding operations to perform fault-tolerant quantum computations resistant to decoherence."
    ],
    advanced: [
      "Stochastic gradient descent optimization algorithms navigate high-dimensional loss landscapes using probabilistic sampling techniques and adaptive learning rates.",
      "Homomorphic encryption schemes enable computations on encrypted data without decryption, preserving privacy in cloud computing environments.",
      "Variational autoencoders learn latent representations by optimizing evidence lower bounds through reparameterization tricks and Monte Carlo estimation.",
      "Categorical homotopy type theory provides foundational frameworks for reasoning about equality, equivalence, and higher-dimensional mathematical structures.",
      "Adversarial perturbations exploit vulnerabilities in neural networks by crafting imperceptible input modifications that cause misclassification errors."
    ]
  }
};

export const getDifficultySettings = (difficulty) => {
  const settings = {
    easy: {
      timeLimit: 60,
      wordsToShow: 50,
      allowBackspace: true,
      showErrors: true,
      description: "Simple words and common phrases"
    },
    medium: {
      timeLimit: 90,
      wordsToShow: 75,
      allowBackspace: true,
      showErrors: true,
      description: "Technical terms and complex sentences"
    },
    hard: {
      timeLimit: 120,
      wordsToShow: 100,
      allowBackspace: false,
      showErrors: false,
      description: "Advanced vocabulary and specialized terminology"
    }
  };
  
  return settings[difficulty] || settings.easy;
};

export const getTestTypeSettings = (testType) => {
  const settings = {
    basic: {
      description: "Fundamental typing skills",
      focusArea: "Basic vocabulary and sentence structure"
    },
    intermediate: {
      description: "Professional typing proficiency",
      focusArea: "Technical terms and complex concepts"
    },
    advanced: {
      description: "Expert-level typing mastery",
      focusArea: "Specialized terminology and academic language"
    }
  };
  
  return settings[testType] || settings.basic;
};
