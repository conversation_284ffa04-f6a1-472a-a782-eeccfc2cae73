// Character sets for different game modes

export const characterSets = {
  // Letters
  lettersLower: 'abcdefghijklmnopqrstuvwxyz'.split(''),
  lettersUpper: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split(''),
  lettersMixed: 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'.split(''),
  
  // Numbers
  numbers: '0123456789'.split(''),
  
  // Symbols - Common keyboard symbols
  symbolsBasic: '!@#$%^&*()'.split(''),
  symbolsExtended: '!@#$%^&*()-_=+[]{}|;:,.<>?/~`'.split(''),
  
  // Mixed sets
  alphanumeric: 'abcdefghijklmnopqrstuvwxyz0123456789'.split(''),
  alphanumericMixed: 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'.split(''),
  basicMixed: 'abcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'.split(''),
  fullMixed: 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()-_=+[]{}|;:,.<>?/~`'.split('')
};

export const gameTypes = {
  // Sequential games (A-Z style)
  azSequential: {
    name: 'A-Z Sequential',
    description: 'Type letters A to Z in order',
    icon: '🔤',
    characterSet: characterSets.lettersUpper,
    isSequential: true,
    category: 'sequential'
  },
  
  // Random games
  randomLettersLower: {
    name: 'Random Lowercase',
    description: 'Type random lowercase letters',
    icon: '🔡',
    characterSet: characterSets.lettersLower,
    isSequential: false,
    category: 'random'
  },
  
  randomLettersUpper: {
    name: 'Random Uppercase',
    description: 'Type random uppercase letters',
    icon: '🔠',
    characterSet: characterSets.lettersUpper,
    isSequential: false,
    category: 'random'
  },
  
  randomLettersMixed: {
    name: 'Random Mixed Case',
    description: 'Type random uppercase and lowercase letters',
    icon: '🔤',
    characterSet: characterSets.lettersMixed,
    isSequential: false,
    category: 'random'
  },
  
  randomNumbers: {
    name: 'Random Numbers',
    description: 'Type random numbers 0-9',
    icon: '🔢',
    characterSet: characterSets.numbers,
    isSequential: false,
    category: 'random'
  },
  
  randomSymbolsBasic: {
    name: 'Basic Symbols',
    description: 'Type common symbols',
    icon: '⚡',
    characterSet: characterSets.symbolsBasic,
    isSequential: false,
    category: 'random'
  },
  
  randomSymbolsExtended: {
    name: 'Extended Symbols',
    description: 'Type all keyboard symbols',
    icon: '🎯',
    characterSet: characterSets.symbolsExtended,
    isSequential: false,
    category: 'random'
  },
  
  randomAlphanumeric: {
    name: 'Letters + Numbers',
    description: 'Type random letters and numbers',
    icon: '🔣',
    characterSet: characterSets.alphanumeric,
    isSequential: false,
    category: 'random'
  },
  
  randomAlphanumericMixed: {
    name: 'Mixed Case + Numbers',
    description: 'Type random letters (mixed case) and numbers',
    icon: '🎲',
    characterSet: characterSets.alphanumericMixed,
    isSequential: false,
    category: 'random'
  },
  
  randomBasicMixed: {
    name: 'Basic Mixed',
    description: 'Letters, numbers, and basic symbols',
    icon: '🎪',
    characterSet: characterSets.basicMixed,
    isSequential: false,
    category: 'random'
  },
  
  randomFullMixed: {
    name: 'Ultimate Challenge',
    description: 'All characters: letters, numbers, symbols',
    icon: '🌟',
    characterSet: characterSets.fullMixed,
    isSequential: false,
    category: 'random'
  }
};

export const gameModes = {
  practice: {
    name: 'Practice',
    description: 'No time limit, focus on accuracy',
    timeLimit: null,
    targetCount: 50,
    icon: '📚'
  },
  
  timed: {
    name: 'Timed Challenge',
    description: '60 seconds to type as many as possible',
    timeLimit: 60,
    targetCount: null,
    icon: '⏱️'
  },
  
  speed: {
    name: 'Speed Test',
    description: 'Type 30 characters as fast as possible',
    timeLimit: null,
    targetCount: 30,
    icon: '⚡'
  },
  
  endurance: {
    name: 'Endurance',
    description: '2 minutes of continuous typing',
    timeLimit: 120,
    targetCount: null,
    icon: '💪'
  }
};

export const difficultySettings = {
  easy: {
    name: 'Easy',
    description: 'Slower pace, more time to think',
    showNext: true,
    allowBackspace: true,
    showErrors: true,
    icon: '🌱'
  },
  
  medium: {
    name: 'Medium',
    description: 'Balanced challenge',
    showNext: true,
    allowBackspace: true,
    showErrors: true,
    icon: '🔥'
  },
  
  hard: {
    name: 'Hard',
    description: 'Fast pace, no mistakes allowed',
    showNext: false,
    allowBackspace: false,
    showErrors: false,
    icon: '⚡'
  }
};

// Helper functions
export const getRandomCharacter = (characterSet) => {
  return characterSet[Math.floor(Math.random() * characterSet.length)];
};

export const generateRandomSequence = (characterSet, length) => {
  return Array.from({ length }, () => getRandomCharacter(characterSet));
};

export const getGameTypesByCategory = (category) => {
  return Object.entries(gameTypes)
    .filter(([_, gameType]) => gameType.category === category)
    .reduce((acc, [key, value]) => {
      acc[key] = value;
      return acc;
    }, {});
};

export const getCharacterDisplayName = (char) => {
  const specialNames = {
    ' ': 'Space',
    '\t': 'Tab',
    '\n': 'Enter',
    '!': 'Exclamation',
    '@': 'At',
    '#': 'Hash',
    '$': 'Dollar',
    '%': 'Percent',
    '^': 'Caret',
    '&': 'Ampersand',
    '*': 'Asterisk',
    '(': 'Left Paren',
    ')': 'Right Paren',
    '-': 'Dash',
    '_': 'Underscore',
    '=': 'Equals',
    '+': 'Plus',
    '[': 'Left Bracket',
    ']': 'Right Bracket',
    '{': 'Left Brace',
    '}': 'Right Brace',
    '|': 'Pipe',
    ';': 'Semicolon',
    ':': 'Colon',
    ',': 'Comma',
    '.': 'Period',
    '<': 'Less Than',
    '>': 'Greater Than',
    '?': 'Question',
    '/': 'Slash',
    '~': 'Tilde',
    '`': 'Backtick'
  };
  
  return specialNames[char] || char;
};
