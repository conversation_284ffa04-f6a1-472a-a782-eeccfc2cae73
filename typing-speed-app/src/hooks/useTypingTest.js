import { useState, useEffect, useCallback, useRef } from 'react';

export const useTypingTest = (testText, settings) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [userInput, setUserInput] = useState('');
  const [errors, setErrors] = useState([]);
  const [startTime, setStartTime] = useState(null);
  const [endTime, setEndTime] = useState(null);
  const [isActive, setIsActive] = useState(false);
  const [timeLeft, setTimeLeft] = useState(settings?.timeLimit || 60);
  const [wpm, setWpm] = useState(0);
  const [accuracy, setAccuracy] = useState(100);
  const [isCompleted, setIsCompleted] = useState(false);
  
  const intervalRef = useRef(null);
  const inputRef = useRef(null);

  // Calculate WPM and accuracy in real-time
  const calculateStats = useCallback(() => {
    if (!startTime || userInput.length === 0) return;

    const timeElapsed = (Date.now() - startTime) / 1000 / 60; // in minutes
    const wordsTyped = userInput.length / 5; // Standard: 5 characters = 1 word
    const currentWpm = Math.round(wordsTyped / timeElapsed) || 0;
    
    // Calculate accuracy
    let correctChars = 0;
    for (let i = 0; i < userInput.length; i++) {
      if (userInput[i] === testText[i]) {
        correctChars++;
      }
    }
    const currentAccuracy = userInput.length > 0 
      ? Math.round((correctChars / userInput.length) * 100) 
      : 100;

    setWpm(currentWpm);
    setAccuracy(currentAccuracy);
  }, [userInput, testText, startTime]);

  // Timer countdown
  useEffect(() => {
    if (isActive && timeLeft > 0 && !isCompleted) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            setIsCompleted(true);
            setIsActive(false);
            setEndTime(Date.now());
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      clearInterval(intervalRef.current);
    }

    return () => clearInterval(intervalRef.current);
  }, [isActive, timeLeft, isCompleted]);

  // Calculate stats when input changes
  useEffect(() => {
    calculateStats();
  }, [calculateStats]);

  // Handle key press
  const handleKeyPress = useCallback((key) => {
    if (isCompleted) return;

    // Start test on first key press
    if (!isActive && !startTime) {
      setIsActive(true);
      setStartTime(Date.now());
    }

    if (key === 'Backspace') {
      if (settings?.allowBackspace && userInput.length > 0) {
        setUserInput(prev => prev.slice(0, -1));
        setCurrentIndex(prev => Math.max(0, prev - 1));
        // Remove error if we're backspacing over it
        setErrors(prev => prev.filter(errorIndex => errorIndex !== userInput.length - 1));
      }
      return;
    }

    // Handle regular character input
    if (key.length === 1 && currentIndex < testText.length) {
      const newInput = userInput + key;
      setUserInput(newInput);
      
      // Check for errors
      if (key !== testText[currentIndex]) {
        setErrors(prev => [...prev, currentIndex]);
      }
      
      setCurrentIndex(prev => prev + 1);
      
      // Check if test is completed
      if (newInput.length === testText.length) {
        setIsCompleted(true);
        setIsActive(false);
        setEndTime(Date.now());
      }
    }
  }, [isCompleted, isActive, startTime, userInput, currentIndex, testText, settings]);

  // Reset test
  const resetTest = useCallback(() => {
    setCurrentIndex(0);
    setUserInput('');
    setErrors([]);
    setStartTime(null);
    setEndTime(null);
    setIsActive(false);
    setTimeLeft(settings?.timeLimit || 60);
    setWpm(0);
    setAccuracy(100);
    setIsCompleted(false);
    clearInterval(intervalRef.current);
    
    // Focus input after reset
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 100);
  }, [settings]);

  // Get final results
  const getResults = useCallback(() => {
    if (!startTime || !endTime) return null;

    const totalTime = (endTime - startTime) / 1000 / 60; // in minutes
    const totalWords = userInput.length / 5;
    const finalWpm = Math.round(totalWords / totalTime) || 0;
    
    const correctChars = userInput.split('').filter((char, index) => 
      char === testText[index]
    ).length;
    
    const finalAccuracy = userInput.length > 0 
      ? Math.round((correctChars / userInput.length) * 100) 
      : 100;

    const errorCount = errors.length;
    const netWpm = Math.max(0, finalWpm - errorCount);

    return {
      wpm: finalWpm,
      netWpm,
      accuracy: finalAccuracy,
      errors: errorCount,
      timeElapsed: Math.round(totalTime * 60), // in seconds
      charactersTyped: userInput.length,
      correctCharacters: correctChars
    };
  }, [startTime, endTime, userInput, testText, errors]);

  // Get character status for styling
  const getCharacterStatus = useCallback((index) => {
    if (index < userInput.length) {
      return userInput[index] === testText[index] ? 'correct' : 'incorrect';
    } else if (index === currentIndex) {
      return 'current';
    }
    return 'pending';
  }, [userInput, testText, currentIndex]);

  return {
    // State
    currentIndex,
    userInput,
    errors,
    isActive,
    isCompleted,
    timeLeft,
    wpm,
    accuracy,
    
    // Methods
    handleKeyPress,
    resetTest,
    getResults,
    getCharacterStatus,
    
    // Refs
    inputRef
  };
};
