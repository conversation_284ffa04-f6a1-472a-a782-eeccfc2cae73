import { useState, useEffect, useCallback, useRef } from 'react';
import { getRandomCharacter, getCharacterDisplayName } from '../data/characterSets';

export const useRandomGame = (gameType, gameMode, difficulty) => {
  const [currentCharacter, setCurrentCharacter] = useState('');
  const [nextCharacter, setNextCharacter] = useState('');
  const [charactersTyped, setCharactersTyped] = useState(0);
  const [correctCharacters, setCorrectCharacters] = useState(0);
  const [errors, setErrors] = useState(0);
  const [isActive, setIsActive] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const [startTime, setStartTime] = useState(null);
  const [endTime, setEndTime] = useState(null);
  const [timeLeft, setTimeLeft] = useState(gameMode.timeLimit || 0);
  const [streak, setStreak] = useState(0);
  const [bestStreak, setBestStreak] = useState(0);
  const [characterTimes, setCharacterTimes] = useState([]);
  const [lastCharacterTime, setLastCharacterTime] = useState(null);
  
  const intervalRef = useRef(null);
  const characterSet = gameType.characterSet;
  const hasTimeLimit = gameMode.timeLimit !== null;
  const hasTargetCount = gameMode.targetCount !== null;

  // Generate initial characters
  useEffect(() => {
    if (gameType.isSequential) {
      setCurrentCharacter(characterSet[0]);
      setNextCharacter(characterSet[1] || '');
    } else {
      const current = getRandomCharacter(characterSet);
      const next = getRandomCharacter(characterSet);
      setCurrentCharacter(current);
      setNextCharacter(next);
    }
  }, [gameType, characterSet]);

  // Timer for timed modes
  useEffect(() => {
    if (isActive && hasTimeLimit && timeLeft > 0 && !isCompleted) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            setIsCompleted(true);
            setIsActive(false);
            setEndTime(Date.now());
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      clearInterval(intervalRef.current);
    }

    return () => clearInterval(intervalRef.current);
  }, [isActive, hasTimeLimit, timeLeft, isCompleted]);

  // Generate next character
  const generateNextCharacter = useCallback(() => {
    if (gameType.isSequential) {
      const currentIndex = characterSet.indexOf(currentCharacter);
      const nextIndex = currentIndex + 1;
      
      if (nextIndex < characterSet.length) {
        return characterSet[nextIndex];
      } else {
        // Restart sequence for sequential games
        return characterSet[0];
      }
    } else {
      // For random games, ensure next character is different from current
      let next;
      do {
        next = getRandomCharacter(characterSet);
      } while (next === currentCharacter && characterSet.length > 1);
      return next;
    }
  }, [gameType, characterSet, currentCharacter]);

  // Handle character input
  const handleCharacterInput = useCallback((inputChar) => {
    if (isCompleted) return;

    // Start game on first input
    if (!isActive && !startTime) {
      setIsActive(true);
      setStartTime(Date.now());
      setLastCharacterTime(Date.now());
    }

    const now = Date.now();
    const timeSinceLastChar = lastCharacterTime ? now - lastCharacterTime : 0;

    if (inputChar === currentCharacter) {
      // Correct character
      setCorrectCharacters(prev => prev + 1);
      setCharactersTyped(prev => prev + 1);
      setStreak(prev => {
        const newStreak = prev + 1;
        setBestStreak(current => Math.max(current, newStreak));
        return newStreak;
      });
      
      // Record timing
      setCharacterTimes(prev => [...prev, timeSinceLastChar]);
      setLastCharacterTime(now);

      // Move to next character
      const next = generateNextCharacter();
      setCurrentCharacter(nextCharacter);
      setNextCharacter(next);

      // Check completion conditions
      if (hasTargetCount && correctCharacters + 1 >= gameMode.targetCount) {
        setIsCompleted(true);
        setIsActive(false);
        setEndTime(now);
      }
    } else {
      // Wrong character
      setErrors(prev => prev + 1);
      setCharactersTyped(prev => prev + 1);
      setStreak(0);
      
      if (!difficulty.allowBackspace) {
        // In hard mode, move to next character even on error
        const next = generateNextCharacter();
        setCurrentCharacter(nextCharacter);
        setNextCharacter(next);
      }
    }
  }, [
    isCompleted, isActive, startTime, currentCharacter, nextCharacter, 
    correctCharacters, lastCharacterTime, hasTargetCount, gameMode.targetCount,
    difficulty.allowBackspace, generateNextCharacter
  ]);

  // Handle keyboard input
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (isCompleted) return;
      
      // Handle all printable characters
      if (e.key.length === 1) {
        e.preventDefault();
        handleCharacterInput(e.key);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [handleCharacterInput, isCompleted]);

  // Reset game
  const resetGame = useCallback(() => {
    setCharactersTyped(0);
    setCorrectCharacters(0);
    setErrors(0);
    setIsActive(false);
    setIsCompleted(false);
    setStartTime(null);
    setEndTime(null);
    setTimeLeft(gameMode.timeLimit || 0);
    setStreak(0);
    setCharacterTimes([]);
    setLastCharacterTime(null);
    clearInterval(intervalRef.current);

    // Reset characters
    if (gameType.isSequential) {
      setCurrentCharacter(characterSet[0]);
      setNextCharacter(characterSet[1] || '');
    } else {
      const current = getRandomCharacter(characterSet);
      const next = getRandomCharacter(characterSet);
      setCurrentCharacter(current);
      setNextCharacter(next);
    }
  }, [gameMode.timeLimit, gameType, characterSet]);

  // Get game results
  const getResults = useCallback(() => {
    if (!startTime || !endTime) return null;

    const totalTime = (endTime - startTime) / 1000; // in seconds
    const accuracy = charactersTyped > 0 ? (correctCharacters / charactersTyped) * 100 : 100;
    const charactersPerSecond = totalTime > 0 ? correctCharacters / totalTime : 0;
    const averageCharacterTime = characterTimes.length > 0 
      ? characterTimes.reduce((sum, time) => sum + time, 0) / characterTimes.length 
      : 0;

    return {
      totalTime: Math.round(totalTime * 100) / 100,
      charactersTyped,
      correctCharacters,
      errors,
      accuracy: Math.round(accuracy * 100) / 100,
      charactersPerSecond: Math.round(charactersPerSecond * 100) / 100,
      averageCharacterTime: Math.round(averageCharacterTime),
      bestStreak,
      gameType: gameType.name,
      gameMode: gameMode.name,
      difficulty: difficulty.name,
      isTargetReached: hasTargetCount ? correctCharacters >= gameMode.targetCount : false
    };
  }, [
    startTime, endTime, charactersTyped, correctCharacters, errors,
    characterTimes, bestStreak, gameType.name, gameMode.name, difficulty.name,
    hasTargetCount, gameMode.targetCount
  ]);

  // Get current stats for display
  const getCurrentStats = useCallback(() => {
    const elapsed = startTime ? (Date.now() - startTime) / 1000 : 0;
    const charactersPerSecond = elapsed > 0 ? correctCharacters / elapsed : 0;
    const accuracy = charactersTyped > 0 ? (correctCharacters / charactersTyped) * 100 : 100;

    return {
      elapsed: Math.round(elapsed * 10) / 10,
      charactersPerSecond: Math.round(charactersPerSecond * 100) / 100,
      accuracy: Math.round(accuracy * 100) / 100,
      streak,
      bestStreak,
      progress: hasTargetCount ? (correctCharacters / gameMode.targetCount) * 100 : 0
    };
  }, [startTime, correctCharacters, charactersTyped, streak, bestStreak, hasTargetCount, gameMode.targetCount]);

  // Get character display info
  const getCharacterInfo = useCallback((char) => {
    return {
      character: char,
      displayName: getCharacterDisplayName(char),
      isSymbol: !/[a-zA-Z0-9]/.test(char),
      isNumber: /[0-9]/.test(char),
      isLetter: /[a-zA-Z]/.test(char),
      isUppercase: /[A-Z]/.test(char),
      isLowercase: /[a-z]/.test(char)
    };
  }, []);

  return {
    // Game state
    currentCharacter,
    nextCharacter,
    charactersTyped,
    correctCharacters,
    errors,
    isActive,
    isCompleted,
    timeLeft,
    streak,
    bestStreak,
    
    // Methods
    handleCharacterInput,
    resetGame,
    getResults,
    getCurrentStats,
    getCharacterInfo,
    
    // Computed values
    hasTimeLimit,
    hasTargetCount,
    targetCount: gameMode.targetCount
  };
};
