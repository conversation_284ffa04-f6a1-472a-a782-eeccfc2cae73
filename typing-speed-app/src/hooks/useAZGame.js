import { useState, useEffect, useCallback, useRef } from 'react';

export const useAZGame = (gameMode = 'normal') => {
  const [currentLetterIndex, setCurrentLetterIndex] = useState(0);
  const [isActive, setIsActive] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const [startTime, setStartTime] = useState(null);
  const [endTime, setEndTime] = useState(null);
  const [errors, setErrors] = useState(0);
  const [timeLeft, setTimeLeft] = useState(60); // 60 seconds for timed mode
  const [letterTimes, setLetterTimes] = useState([]); // Track time for each letter
  const [streak, setStreak] = useState(0);
  const [bestStreak, setBestStreak] = useState(0);
  
  const intervalRef = useRef(null);
  const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
  const currentLetter = alphabet[currentLetterIndex];

  // Game modes: 'normal' (no time limit), 'timed' (60 seconds), 'speed' (fastest completion)
  const isTimedMode = gameMode === 'timed';

  // Timer for timed mode
  useEffect(() => {
    if (isActive && isTimedMode && timeLeft > 0 && !isCompleted) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            setIsCompleted(true);
            setIsActive(false);
            setEndTime(Date.now());
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      clearInterval(intervalRef.current);
    }

    return () => clearInterval(intervalRef.current);
  }, [isActive, isTimedMode, timeLeft, isCompleted]);

  // Handle letter input (keyboard or button click)
  const handleLetterInput = useCallback((inputLetter) => {
    if (isCompleted) return;

    // Start game on first input
    if (!isActive && !startTime) {
      setIsActive(true);
      setStartTime(Date.now());
    }

    const expectedLetter = alphabet[currentLetterIndex];
    const now = Date.now();

    if (inputLetter.toUpperCase() === expectedLetter) {
      // Correct letter
      const letterTime = startTime ? now - startTime - (letterTimes.reduce((sum, time) => sum + time, 0)) : 0;
      setLetterTimes(prev => [...prev, letterTime]);
      setCurrentLetterIndex(prev => prev + 1);
      setStreak(prev => {
        const newStreak = prev + 1;
        setBestStreak(current => Math.max(current, newStreak));
        return newStreak;
      });

      // Check if game is completed
      if (currentLetterIndex + 1 >= alphabet.length) {
        setIsCompleted(true);
        setIsActive(false);
        setEndTime(now);
      }
    } else {
      // Wrong letter
      setErrors(prev => prev + 1);
      setStreak(0);
    }
  }, [isCompleted, isActive, startTime, currentLetterIndex, alphabet, letterTimes]);

  // Handle keyboard input
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (isCompleted) return;
      
      // Only handle letter keys
      if (e.key.match(/^[a-zA-Z]$/)) {
        e.preventDefault();
        handleLetterInput(e.key);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [handleLetterInput, isCompleted]);

  // Reset game
  const resetGame = useCallback(() => {
    setCurrentLetterIndex(0);
    setIsActive(false);
    setIsCompleted(false);
    setStartTime(null);
    setEndTime(null);
    setErrors(0);
    setTimeLeft(60);
    setLetterTimes([]);
    setStreak(0);
    clearInterval(intervalRef.current);
  }, []);

  // Get game results
  const getResults = useCallback(() => {
    if (!startTime || !endTime) return null;

    const totalTime = (endTime - startTime) / 1000; // in seconds
    const lettersCompleted = currentLetterIndex;
    const accuracy = lettersCompleted > 0 ? ((lettersCompleted / (lettersCompleted + errors)) * 100) : 100;
    const lettersPerSecond = totalTime > 0 ? lettersCompleted / totalTime : 0;
    const averageLetterTime = letterTimes.length > 0 ? letterTimes.reduce((sum, time) => sum + time, 0) / letterTimes.length : 0;

    return {
      totalTime: Math.round(totalTime * 100) / 100,
      lettersCompleted,
      errors,
      accuracy: Math.round(accuracy * 100) / 100,
      lettersPerSecond: Math.round(lettersPerSecond * 100) / 100,
      averageLetterTime: Math.round(averageLetterTime),
      bestStreak,
      gameMode,
      isFullCompletion: lettersCompleted === alphabet.length
    };
  }, [startTime, endTime, currentLetterIndex, errors, letterTimes, bestStreak, gameMode, alphabet.length]);

  // Get letter status for styling
  const getLetterStatus = useCallback((letter) => {
    const letterIndex = alphabet.indexOf(letter);
    
    if (letterIndex < currentLetterIndex) {
      return 'completed';
    } else if (letterIndex === currentLetterIndex) {
      return 'current';
    } else {
      return 'pending';
    }
  }, [currentLetterIndex, alphabet]);

  // Calculate progress percentage
  const getProgress = useCallback(() => {
    return (currentLetterIndex / alphabet.length) * 100;
  }, [currentLetterIndex, alphabet.length]);

  // Get current stats for display
  const getCurrentStats = useCallback(() => {
    const elapsed = startTime ? (Date.now() - startTime) / 1000 : 0;
    const lettersPerSecond = elapsed > 0 ? currentLetterIndex / elapsed : 0;
    const accuracy = (currentLetterIndex + errors) > 0 ? (currentLetterIndex / (currentLetterIndex + errors)) * 100 : 100;

    return {
      elapsed: Math.round(elapsed * 10) / 10,
      lettersPerSecond: Math.round(lettersPerSecond * 100) / 100,
      accuracy: Math.round(accuracy * 100) / 100,
      streak,
      bestStreak
    };
  }, [startTime, currentLetterIndex, errors, streak, bestStreak]);

  return {
    // Game state
    currentLetter,
    currentLetterIndex,
    isActive,
    isCompleted,
    timeLeft,
    errors,
    streak,
    bestStreak,
    alphabet,
    
    // Methods
    handleLetterInput,
    resetGame,
    getResults,
    getLetterStatus,
    getProgress,
    getCurrentStats,
    
    // Computed values
    isTimedMode
  };
};
