.random-game {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

/* Instructions Screen */
.game-instructions {
  text-align: center;
  background: var(--bg-card);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: 3rem;
  box-shadow: var(--shadow-lg);
}

.instruction-header {
  margin-bottom: 3rem;
}

.mode-icon {
  font-size: 4rem;
  display: block;
  margin-bottom: 1rem;
  animation: bounce 2s ease-in-out infinite;
}

.instruction-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--text-accent) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.instruction-header p {
  font-size: 1.2rem;
  color: var(--text-secondary);
  opacity: 0.9;
}

.instruction-content {
  display: grid;
  gap: 2rem;
  margin-bottom: 3rem;
  text-align: left;
}

.instruction-item {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  background: var(--bg-tertiary);
  padding: 1.5rem;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
}

.instruction-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.instruction-item h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.instruction-item p {
  font-size: 0.95rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

.instruction-actions {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
}

.start-game-button {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: 1rem 2.5rem;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
}

.start-game-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  animation: glow 2s ease-in-out infinite;
}

/* Game Header */
.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.back-button {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.back-button:hover {
  background: var(--bg-card);
  color: var(--text-primary);
  border-color: var(--border-secondary);
}

.mode-badge {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-lg);
  font-size: 1rem;
  font-weight: 600;
}

/* Stats Bar */
.game-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-item {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: 1rem;
  text-align: center;
  transition: all var(--transition-normal);
}

.stat-item:hover {
  border-color: var(--border-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-label {
  display: block;
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.stat-target {
  font-size: 1rem;
  color: var(--text-muted);
}

.streak-hot {
  color: var(--warning-color);
  animation: pulse 1s ease-in-out infinite;
}

.has-errors {
  color: var(--error-color);
}

.time-warning {
  color: var(--warning-color);
  animation: pulse 1s ease-in-out infinite;
}

.warning {
  color: var(--warning-color);
}

.good {
  color: var(--success-color);
}

/* Progress Bar */
.progress-container {
  margin-bottom: 2rem;
}

.progress-bar {
  width: 100%;
  height: 12px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--success-color) 100%);
  border-radius: var(--radius-md);
  transition: width var(--transition-normal);
}

.progress-text {
  display: block;
  text-align: center;
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Character Display */
.character-area {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
  background: var(--bg-card);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: 3rem;
}

.current-character-section {
  text-align: center;
}

.next-character-section {
  text-align: center;
  opacity: 0.7;
}

.character-label {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
  font-weight: 500;
}

.character-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.character-display .char {
  font-size: 6rem;
  font-weight: 700;
  font-family: 'JetBrains Mono', monospace;
  color: var(--text-primary);
  text-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  transition: all var(--transition-normal);
}

.character-display.current .char {
  animation: characterPulse 2s ease-in-out infinite;
  color: var(--primary-color);
}

.character-display.next .char {
  font-size: 3rem;
  color: var(--text-muted);
}

.character-display .char-name {
  font-size: 1rem;
  color: var(--text-secondary);
  background: var(--bg-tertiary);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  font-weight: 500;
}

/* Character type styling */
.character-display.symbol .char {
  color: var(--warning-color);
}

.character-display.number .char {
  color: var(--secondary-color);
}

.character-display.letter .char {
  color: var(--primary-color);
}

.character-display.uppercase .char {
  text-transform: uppercase;
}

@keyframes characterPulse {
  0%, 100% {
    transform: scale(1);
    text-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }
  50% {
    transform: scale(1.05);
    text-shadow: 0 0 30px rgba(99, 102, 241, 0.6);
  }
}

/* Character Set Preview */
.character-set-preview {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.character-set-preview h4 {
  font-size: 1.1rem;
  color: var(--text-primary);
  margin-bottom: 1rem;
  text-align: center;
}

.character-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
}

.preview-char {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  padding: 0.5rem;
  font-family: 'JetBrains Mono', monospace;
  font-weight: 600;
  color: var(--text-primary);
  min-width: 2rem;
  text-align: center;
}

.more-chars {
  color: var(--text-muted);
  font-style: italic;
  padding: 0.5rem;
}

/* Game Status */
.game-status {
  text-align: center;
  margin-bottom: 2rem;
}

.status-message {
  font-size: 1.1rem;
  color: var(--text-secondary);
  background: var(--bg-tertiary);
  padding: 1rem 1.5rem;
  border-radius: var(--radius-md);
  display: inline-block;
  border: 1px solid var(--border-primary);
}

.completion-message {
  color: var(--success-color) !important;
  background: rgba(34, 197, 94, 0.1) !important;
  border-color: rgba(34, 197, 94, 0.3) !important;
  animation: bounce 2s ease-in-out infinite;
}

/* Game Actions */
.game-actions {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
}

.reset-button,
.continue-button {
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  border: 2px solid transparent;
}

.reset-button {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border-color: var(--border-primary);
}

.reset-button:hover {
  background: var(--warning-color);
  color: white;
  border-color: var(--warning-color);
}

.continue-button {
  background: linear-gradient(135deg, var(--success-color) 0%, var(--secondary-color) 100%);
  color: white;
}

.continue-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
  .game-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .game-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .character-area {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 2rem;
  }

  .character-display .char {
    font-size: 4rem;
  }

  .character-display.next .char {
    font-size: 2.5rem;
  }

  .instruction-actions {
    flex-direction: column;
  }

  .game-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .game-stats {
    grid-template-columns: 1fr;
  }

  .character-display .char {
    font-size: 3rem;
  }

  .character-display.next .char {
    font-size: 2rem;
  }

  .instruction-header h2 {
    font-size: 2rem;
  }

  .mode-icon {
    font-size: 3rem;
  }

  .character-area {
    padding: 1.5rem;
  }
}
