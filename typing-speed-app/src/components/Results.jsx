import './Results.css';

const Results = ({ results, difficulty, testType, onRetakeTest, onNewTest }) => {
  if (!results) return null;

  const getPerformanceLevel = (wpm, accuracy) => {
    if (accuracy < 85) return { level: 'Needs Improvement', color: 'error', icon: '📚' };
    if (wpm < 30) return { level: 'Beginner', color: 'warning', icon: '🌱' };
    if (wpm < 50) return { level: 'Intermediate', color: 'primary', icon: '💪' };
    if (wpm < 70) return { level: 'Advanced', color: 'success', icon: '🚀' };
    return { level: 'Expert', color: 'success', icon: '👑' };
  };

  const getAccuracyGrade = (accuracy) => {
    if (accuracy >= 98) return { grade: 'A+', color: 'success' };
    if (accuracy >= 95) return { grade: 'A', color: 'success' };
    if (accuracy >= 90) return { grade: 'B+', color: 'primary' };
    if (accuracy >= 85) return { grade: 'B', color: 'primary' };
    if (accuracy >= 80) return { grade: 'C+', color: 'warning' };
    if (accuracy >= 75) return { grade: 'C', color: 'warning' };
    return { grade: 'D', color: 'error' };
  };

  const performance = getPerformanceLevel(results.wpm, results.accuracy);
  const accuracyGrade = getAccuracyGrade(results.accuracy);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="results fade-in">
      <div className="results-header">
        <div className="performance-badge">
          <span className="performance-icon">{performance.icon}</span>
          <div className="performance-text">
            <h2>Test Complete!</h2>
            <p className={`performance-level ${performance.color}`}>
              {performance.level} Typist
            </p>
          </div>
        </div>
      </div>

      <div className="results-grid">
        {/* Main Stats */}
        <div className="stats-section">
          <h3>Performance Metrics</h3>
          <div className="main-stats">
            <div className="stat-card primary">
              <div className="stat-icon">⚡</div>
              <div className="stat-content">
                <span className="stat-value">{results.wpm}</span>
                <span className="stat-label">Words Per Minute</span>
              </div>
            </div>

            <div className="stat-card success">
              <div className="stat-icon">🎯</div>
              <div className="stat-content">
                <span className="stat-value">{results.accuracy}%</span>
                <span className="stat-label">Accuracy</span>
                <span className={`accuracy-grade ${accuracyGrade.color}`}>
                  Grade: {accuracyGrade.grade}
                </span>
              </div>
            </div>

            <div className="stat-card secondary">
              <div className="stat-icon">🏆</div>
              <div className="stat-content">
                <span className="stat-value">{results.netWpm}</span>
                <span className="stat-label">Net WPM</span>
                <span className="stat-note">After error penalty</span>
              </div>
            </div>
          </div>
        </div>

        {/* Detailed Stats */}
        <div className="details-section">
          <h3>Detailed Statistics</h3>
          <div className="detail-stats">
            <div className="detail-item">
              <span className="detail-label">⏱️ Time Elapsed</span>
              <span className="detail-value">{formatTime(results.timeElapsed)}</span>
            </div>
            
            <div className="detail-item">
              <span className="detail-label">📝 Characters Typed</span>
              <span className="detail-value">{results.charactersTyped}</span>
            </div>
            
            <div className="detail-item">
              <span className="detail-label">✅ Correct Characters</span>
              <span className="detail-value">{results.correctCharacters}</span>
            </div>
            
            <div className="detail-item">
              <span className="detail-label">❌ Errors Made</span>
              <span className="detail-value">{results.errors}</span>
            </div>
            
            <div className="detail-item">
              <span className="detail-label">🎚️ Difficulty Level</span>
              <span className="detail-value capitalize">{difficulty}</span>
            </div>
            
            <div className="detail-item">
              <span className="detail-label">📋 Test Type</span>
              <span className="detail-value capitalize">{testType}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Visualization */}
      <div className="progress-section">
        <h3>Performance Breakdown</h3>
        <div className="progress-bars">
          <div className="progress-item">
            <div className="progress-header">
              <span>Speed (WPM)</span>
              <span>{results.wpm}/100</span>
            </div>
            <div className="progress-bar">
              <div 
                className="progress-fill speed" 
                style={{ width: `${Math.min(results.wpm, 100)}%` }}
              ></div>
            </div>
          </div>
          
          <div className="progress-item">
            <div className="progress-header">
              <span>Accuracy</span>
              <span>{results.accuracy}%</span>
            </div>
            <div className="progress-bar">
              <div 
                className="progress-fill accuracy" 
                style={{ width: `${results.accuracy}%` }}
              ></div>
            </div>
          </div>
          
          <div className="progress-item">
            <div className="progress-header">
              <span>Consistency</span>
              <span>{Math.max(0, 100 - results.errors * 2)}%</span>
            </div>
            <div className="progress-bar">
              <div 
                className="progress-fill consistency" 
                style={{ width: `${Math.max(0, 100 - results.errors * 2)}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="results-actions">
        <button className="action-button secondary" onClick={onRetakeTest}>
          🔄 Retake Same Test
        </button>
        <button className="action-button primary" onClick={onNewTest}>
          🎯 Try Different Level
        </button>
      </div>

      {/* Tips Section */}
      <div className="tips-section">
        <h3>💡 Tips for Improvement</h3>
        <div className="tips-grid">
          {results.accuracy < 90 && (
            <div className="tip-card">
              <span className="tip-icon">🎯</span>
              <p>Focus on accuracy over speed. Slow down and make fewer mistakes.</p>
            </div>
          )}
          {results.wpm < 40 && (
            <div className="tip-card">
              <span className="tip-icon">⚡</span>
              <p>Practice touch typing to increase your speed naturally.</p>
            </div>
          )}
          {results.errors > 5 && (
            <div className="tip-card">
              <span className="tip-icon">🔍</span>
              <p>Take your time to read ahead and anticipate the next words.</p>
            </div>
          )}
          <div className="tip-card">
            <span className="tip-icon">💪</span>
            <p>Regular practice is key to improving your typing skills!</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Results;
