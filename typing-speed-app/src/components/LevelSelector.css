.level-selector {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.header-content {
  text-align: center;
  flex: 1;
}

.header-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--text-accent) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-content p {
  font-size: 1.2rem;
  color: var(--text-secondary);
  opacity: 0.9;
}

.settings-button {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all var(--transition-fast);
  font-weight: 500;
}

.settings-button:hover {
  background: var(--bg-card);
  color: var(--text-primary);
  border-color: var(--border-secondary);
  transform: translateY(-1px);
}

/* Settings Panel */
.settings-panel {
  background: var(--bg-card);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: 2rem;
  margin-bottom: 3rem;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.settings-panel h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  text-align: center;
}

.settings-options {
  display: grid;
  gap: 1.5rem;
}

.setting-item {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  transition: all var(--transition-normal);
}

.setting-item:hover {
  border-color: var(--border-secondary);
  box-shadow: var(--shadow-sm);
}

.setting-label {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-primary);
  cursor: pointer;
  margin-bottom: 0.5rem;
}

.setting-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  background: var(--bg-secondary);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-sm);
  position: relative;
  transition: all var(--transition-fast);
}

.checkmark::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.setting-label input[type="checkbox"]:checked + .checkmark {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.setting-label input[type="checkbox"]:checked + .checkmark::after {
  opacity: 1;
}

.setting-description {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin: 0;
  padding-left: 3rem;
}

/* Mode Selection */
.mode-selection {
  margin-bottom: 3rem;
}

.mode-selection h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  text-align: center;
}

.mode-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  max-width: 900px;
  margin: 0 auto;
}

.mode-card {
  background: var(--bg-card);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: 2rem;
  text-align: center;
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transform: translateY(0);
}

.mode-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(99, 102, 241, 0.05) 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.mode-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left var(--transition-slow);
}

.mode-card:hover::before {
  left: 100%;
}

.mode-card:hover {
  border-color: var(--border-accent);
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.mode-card:hover::after {
  opacity: 1;
}

.mode-card.selected {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, var(--bg-card) 0%, rgba(99, 102, 241, 0.15) 100%);
  box-shadow: 0 0 25px rgba(99, 102, 241, 0.4);
  transform: translateY(-2px);
}

.mode-card.selected::after {
  opacity: 1;
  background: linear-gradient(135deg, transparent 0%, rgba(99, 102, 241, 0.1) 100%);
}

.mode-card.selected .mode-icon {
  animation: selectedPulse 2s ease-in-out infinite;
}

@keyframes selectedPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.mode-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.mode-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.mode-description {
  font-size: 0.9rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.selection-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
}

.selection-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  text-align: center;
}

.options-grid {
  display: grid;
  gap: 1rem;
}

.random-game-grid {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.option-card {
  background: var(--bg-card);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  text-align: center;
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transform: translateY(0);
}

.option-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-light) 100%);
  transform: scaleX(0);
  transition: transform var(--transition-normal);
  transform-origin: left;
}

.option-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left var(--transition-slow);
}

.option-card:hover::before {
  left: 100%;
}

.option-card:hover {
  border-color: var(--border-accent);
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.option-card:hover::before {
  transform: scaleX(1);
}

.option-card.selected {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, var(--bg-card) 0%, rgba(99, 102, 241, 0.12) 100%);
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
  transform: translateY(-1px);
}

.option-card.selected::before {
  transform: scaleX(1);
}

.option-card.selected .option-icon {
  animation: optionPulse 2s ease-in-out infinite;
}

@keyframes optionPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.option-icon {
  font-size: 2.5rem;
}

.difficulty-stars {
  font-size: 1rem;
  color: var(--warning-color);
  font-weight: bold;
  letter-spacing: 0.1em;
}

/* Difficulty-specific styling */
.difficulty-success {
  border-color: var(--success-color);
}

.difficulty-success.selected {
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.4);
  background: linear-gradient(135deg, var(--bg-card) 0%, rgba(34, 197, 94, 0.1) 100%);
}

.difficulty-warning {
  border-color: var(--warning-color);
}

.difficulty-warning.selected {
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.4);
  background: linear-gradient(135deg, var(--bg-card) 0%, rgba(245, 158, 11, 0.1) 100%);
}

.difficulty-error {
  border-color: var(--error-color);
}

.difficulty-error.selected {
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.4);
  background: linear-gradient(135deg, var(--bg-card) 0%, rgba(239, 68, 68, 0.1) 100%);
}

.option-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.option-description {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 0.75rem;
}

.option-details {
  display: flex;
  justify-content: center;
  gap: 1rem;
  font-size: 0.85rem;
  color: var(--text-muted);
}

.option-focus {
  font-size: 0.85rem;
  color: var(--text-muted);
  font-style: italic;
}

.start-section {
  text-align: center;
}

.selected-summary {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.selected-summary h4 {
  font-size: 1.1rem;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.summary-details {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  color: var(--text-secondary);
  background: var(--bg-tertiary);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
}

.start-button {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: 1rem 2.5rem;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0 auto;
  box-shadow: var(--shadow-md);
}

.start-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  animation: glow 2s ease-in-out infinite;
}

.start-button:active {
  transform: translateY(0);
}

.start-icon {
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .selector-header {
    flex-direction: column;
    text-align: center;
  }

  .selection-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .mode-options {
    grid-template-columns: 1fr;
  }

  .random-game-grid {
    grid-template-columns: 1fr;
  }

  .header-content h2 {
    font-size: 2rem;
  }

  .summary-details {
    flex-direction: column;
    gap: 1rem;
  }

  .start-button {
    width: 100%;
    justify-content: center;
  }

  .settings-panel {
    padding: 1.5rem;
  }

  .setting-description {
    padding-left: 0;
    margin-top: 0.5rem;
  }
}

@media (max-width: 480px) {
  .selector-header h2 {
    font-size: 1.75rem;
  }
  
  .option-card {
    padding: 1rem;
  }
  
  .option-icon {
    font-size: 2rem;
  }
}
