.level-selector {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

.selector-header {
  text-align: center;
  margin-bottom: 3rem;
}

.selector-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--text-accent) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.selector-header p {
  font-size: 1.2rem;
  color: var(--text-secondary);
  opacity: 0.9;
}

/* Mode Selection */
.mode-selection {
  margin-bottom: 3rem;
}

.mode-selection h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  text-align: center;
}

.mode-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  max-width: 900px;
  margin: 0 auto;
}

.mode-card {
  background: var(--bg-card);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: 2rem;
  text-align: center;
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.mode-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left var(--transition-slow);
}

.mode-card:hover::before {
  left: 100%;
}

.mode-card:hover {
  border-color: var(--border-accent);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.mode-card.selected {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, var(--bg-card) 0%, rgba(99, 102, 241, 0.1) 100%);
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
}

.mode-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.mode-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.mode-description {
  font-size: 0.9rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.selection-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
}

.selection-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  text-align: center;
}

.options-grid {
  display: grid;
  gap: 1rem;
}

.random-game-grid {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.option-card {
  background: var(--bg-card);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  text-align: center;
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.option-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left var(--transition-slow);
}

.option-card:hover::before {
  left: 100%;
}

.option-card:hover {
  border-color: var(--border-accent);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.option-card.selected {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, var(--bg-card) 0%, rgba(99, 102, 241, 0.1) 100%);
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
}

.option-icon {
  font-size: 2.5rem;
  margin-bottom: 0.75rem;
}

.option-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.option-description {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 0.75rem;
}

.option-details {
  display: flex;
  justify-content: center;
  gap: 1rem;
  font-size: 0.85rem;
  color: var(--text-muted);
}

.option-focus {
  font-size: 0.85rem;
  color: var(--text-muted);
  font-style: italic;
}

.start-section {
  text-align: center;
}

.selected-summary {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.selected-summary h4 {
  font-size: 1.1rem;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.summary-details {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  color: var(--text-secondary);
  background: var(--bg-tertiary);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
}

.start-button {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: 1rem 2.5rem;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0 auto;
  box-shadow: var(--shadow-md);
}

.start-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  animation: glow 2s ease-in-out infinite;
}

.start-button:active {
  transform: translateY(0);
}

.start-icon {
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .selection-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .mode-options {
    grid-template-columns: 1fr;
  }

  .random-game-grid {
    grid-template-columns: 1fr;
  }

  .selector-header h2 {
    font-size: 2rem;
  }

  .summary-details {
    flex-direction: column;
    gap: 1rem;
  }

  .start-button {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .selector-header h2 {
    font-size: 1.75rem;
  }
  
  .option-card {
    padding: 1rem;
  }
  
  .option-icon {
    font-size: 2rem;
  }
}
