.typing-test {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.back-button {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.back-button:hover {
  background: var(--bg-card);
  color: var(--text-primary);
  border-color: var(--border-secondary);
}

.test-info {
  display: flex;
  gap: 1rem;
}

.difficulty-badge,
.test-type-badge {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: capitalize;
}

.test-type-badge {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--success-color) 100%);
}

.stats-bar {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-item {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  text-align: center;
  transition: all var(--transition-normal);
}

.stat-item:hover {
  border-color: var(--border-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-label {
  display: block;
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.stat-value {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  transition: color var(--transition-fast);
}

.stat-value.warning {
  color: var(--warning-color);
  animation: pulse 1s ease-in-out infinite;
}

.stat-value.good {
  color: var(--success-color);
}

.progress-container {
  margin-bottom: 2rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-light) 100%);
  border-radius: var(--radius-md);
  transition: width var(--transition-fast);
}

.progress-text {
  display: block;
  text-align: center;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.typing-area {
  background: var(--bg-card);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: 2rem;
  margin-bottom: 2rem;
  position: relative;
  min-height: 200px;
  transition: border-color var(--transition-fast);
}

.typing-area:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.text-display {
  font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  font-size: 1.25rem;
  line-height: 1.8;
  letter-spacing: 0.02em;
  word-wrap: break-word;
  user-select: none;
}

.character {
  position: relative;
  transition: all var(--transition-fast);
}

.character.correct {
  color: var(--success-color);
  background: rgba(34, 197, 94, 0.1);
}

.character.incorrect {
  color: var(--error-color);
  background: rgba(239, 68, 68, 0.2);
  border-radius: 2px;
}

.character.current {
  background: var(--primary-color);
  color: white;
  border-radius: 2px;
  animation: blink 1s ease-in-out infinite;
}

.character.pending {
  color: var(--text-muted);
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.hidden-input {
  position: absolute;
  left: -9999px;
  opacity: 0;
}

.instructions {
  text-align: center;
  margin-bottom: 2rem;
}

.instructions p {
  font-size: 1rem;
  color: var(--text-secondary);
  background: var(--bg-tertiary);
  padding: 1rem 1.5rem;
  border-radius: var(--radius-md);
  display: inline-block;
}

.completion-message {
  color: var(--success-color) !important;
  background: rgba(34, 197, 94, 0.1) !important;
  border: 1px solid rgba(34, 197, 94, 0.3);
  animation: pulse 2s ease-in-out infinite;
}

.test-actions {
  text-align: center;
}

.reset-button {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.reset-button:hover {
  background: var(--warning-color);
  color: white;
  border-color: var(--warning-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .test-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .stats-bar {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-item {
    padding: 1rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .text-display {
    font-size: 1.1rem;
  }

  .typing-area {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .typing-area {
    padding: 1rem;
  }

  .text-display {
    font-size: 1rem;
    line-height: 1.6;
  }

  .test-info {
    flex-direction: column;
    gap: 0.5rem;
  }
}
