.virtual-keyboard {
  background: var(--bg-card);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  margin-top: 2rem;
  box-shadow: var(--shadow-md);
}

.keyboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-primary);
}

.keyboard-header h4 {
  font-size: 1.1rem;
  color: var(--text-primary);
  margin: 0;
  font-weight: 600;
}

.keyboard-indicators {
  display: flex;
  gap: 0.5rem;
}

.shift-indicator {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-weight: 600;
  animation: pulse 1s ease-in-out infinite;
}

.keyboard-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.keyboard-row {
  display: flex;
  gap: 0.25rem;
  justify-content: center;
}

.virtual-key {
  position: relative;
  background: var(--bg-tertiary);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  font-family: 'JetBrains Mono', monospace;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 2.5rem;
  min-width: 2.5rem;
  padding: 0.25rem;
  user-select: none;
}

.virtual-key:hover {
  background: var(--bg-input);
  border-color: var(--border-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.virtual-key:active,
.virtual-key.pressed {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(0);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.virtual-key.highlighted {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
  border-color: var(--primary-color);
  animation: keyHighlight 2s ease-in-out infinite;
  box-shadow: 0 0 15px rgba(99, 102, 241, 0.5);
}

.virtual-key.active {
  background: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

.virtual-key.disabled {
  background: var(--bg-secondary);
  color: var(--text-muted);
  border-color: var(--border-primary);
  cursor: not-allowed;
  opacity: 0.5;
}

.virtual-key.disabled:hover {
  transform: none;
  box-shadow: none;
}

.special-key {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  font-size: 0.75rem;
}

.special-key:hover {
  background: var(--bg-tertiary);
}

/* Key width variations */
.key-wide {
  min-width: 4rem;
}

.key-extra-wide {
  min-width: 5.5rem;
}

.key-space {
  min-width: 12rem;
}

/* Key content styling */
.key-main {
  font-size: 1rem;
  line-height: 1;
}

.key-shift {
  font-size: 0.7rem;
  color: var(--text-muted);
  position: absolute;
  top: 0.2rem;
  right: 0.2rem;
  line-height: 1;
}

.virtual-key.highlighted .key-shift {
  color: rgba(255, 255, 255, 0.8);
}

/* Animations */
@keyframes keyHighlight {
  0%, 100% {
    box-shadow: 0 0 15px rgba(99, 102, 241, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 25px rgba(99, 102, 241, 0.8);
    transform: scale(1.05);
  }
}

.keyboard-footer {
  padding-top: 0.75rem;
  border-top: 1px solid var(--border-primary);
}

.keyboard-tips {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem;
  color: var(--text-secondary);
  flex-wrap: wrap;
  gap: 1rem;
}

.highlight-tip {
  background: var(--bg-tertiary);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
}

.highlight-tip strong {
  color: var(--primary-color);
  font-family: 'JetBrains Mono', monospace;
}

/* Responsive Design */
@media (max-width: 768px) {
  .virtual-keyboard {
    padding: 1rem;
    margin-top: 1.5rem;
  }
  
  .keyboard-container {
    gap: 0.3rem;
  }
  
  .keyboard-row {
    gap: 0.2rem;
  }
  
  .virtual-key {
    min-height: 2.2rem;
    min-width: 2.2rem;
    font-size: 0.8rem;
  }
  
  .key-main {
    font-size: 0.9rem;
  }
  
  .key-shift {
    font-size: 0.6rem;
  }
  
  .key-wide {
    min-width: 3.5rem;
  }
  
  .key-extra-wide {
    min-width: 4.5rem;
  }
  
  .key-space {
    min-width: 8rem;
  }
  
  .keyboard-tips {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .virtual-key {
    min-height: 2rem;
    min-width: 2rem;
    font-size: 0.75rem;
  }
  
  .key-main {
    font-size: 0.8rem;
  }
  
  .key-shift {
    font-size: 0.55rem;
  }
  
  .key-wide {
    min-width: 3rem;
  }
  
  .key-extra-wide {
    min-width: 4rem;
  }
  
  .key-space {
    min-width: 6rem;
  }
  
  .keyboard-header h4 {
    font-size: 1rem;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .virtual-key {
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
  
  .virtual-key:hover {
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), var(--shadow-sm);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .virtual-key {
    border-width: 3px;
  }
  
  .virtual-key.highlighted {
    border-width: 4px;
  }
}
