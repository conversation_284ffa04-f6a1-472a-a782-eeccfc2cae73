import { useState, useEffect } from 'react';
import './VirtualKeyboard.css';

const VirtualKeyboard = ({ 
  onKeyPress, 
  highlightKey = null, 
  disabledKeys = [], 
  showKeyboard = true,
  keyboardLayout = 'qwerty' 
}) => {
  const [pressedKey, setPressedKey] = useState(null);
  const [shiftPressed, setShiftPressed] = useState(false);

  // QWERTY keyboard layout
  const keyboardRows = [
    // Number row
    [
      { key: '`', shift: '~', code: 'Backquote' },
      { key: '1', shift: '!', code: 'Digit1' },
      { key: '2', shift: '@', code: 'Digit2' },
      { key: '3', shift: '#', code: 'Digit3' },
      { key: '4', shift: '$', code: 'Digit4' },
      { key: '5', shift: '%', code: 'Digit5' },
      { key: '6', shift: '^', code: 'Digit6' },
      { key: '7', shift: '&', code: 'Digit7' },
      { key: '8', shift: '*', code: 'Digit8' },
      { key: '9', shift: '(', code: 'Digit9' },
      { key: '0', shift: ')', code: 'Digit0' },
      { key: '-', shift: '_', code: 'Minus' },
      { key: '=', shift: '+', code: 'Equal' },
      { key: 'Backspace', width: 'wide', code: 'Backspace' }
    ],
    // Top letter row
    [
      { key: 'Tab', width: 'wide', code: 'Tab' },
      { key: 'q', shift: 'Q', code: 'KeyQ' },
      { key: 'w', shift: 'W', code: 'KeyW' },
      { key: 'e', shift: 'E', code: 'KeyE' },
      { key: 'r', shift: 'R', code: 'KeyR' },
      { key: 't', shift: 'T', code: 'KeyT' },
      { key: 'y', shift: 'Y', code: 'KeyY' },
      { key: 'u', shift: 'U', code: 'KeyU' },
      { key: 'i', shift: 'I', code: 'KeyI' },
      { key: 'o', shift: 'O', code: 'KeyO' },
      { key: 'p', shift: 'P', code: 'KeyP' },
      { key: '[', shift: '{', code: 'BracketLeft' },
      { key: ']', shift: '}', code: 'BracketRight' },
      { key: '\\', shift: '|', code: 'Backslash' }
    ],
    // Middle letter row
    [
      { key: 'Caps', width: 'wide', code: 'CapsLock' },
      { key: 'a', shift: 'A', code: 'KeyA' },
      { key: 's', shift: 'S', code: 'KeyS' },
      { key: 'd', shift: 'D', code: 'KeyD' },
      { key: 'f', shift: 'F', code: 'KeyF' },
      { key: 'g', shift: 'G', code: 'KeyG' },
      { key: 'h', shift: 'H', code: 'KeyH' },
      { key: 'j', shift: 'J', code: 'KeyJ' },
      { key: 'k', shift: 'K', code: 'KeyK' },
      { key: 'l', shift: 'L', code: 'KeyL' },
      { key: ';', shift: ':', code: 'Semicolon' },
      { key: "'", shift: '"', code: 'Quote' },
      { key: 'Enter', width: 'wide', code: 'Enter' }
    ],
    // Bottom letter row
    [
      { key: 'Shift', width: 'extra-wide', code: 'ShiftLeft' },
      { key: 'z', shift: 'Z', code: 'KeyZ' },
      { key: 'x', shift: 'X', code: 'KeyX' },
      { key: 'c', shift: 'C', code: 'KeyC' },
      { key: 'v', shift: 'V', code: 'KeyV' },
      { key: 'b', shift: 'B', code: 'KeyB' },
      { key: 'n', shift: 'N', code: 'KeyN' },
      { key: 'm', shift: 'M', code: 'KeyM' },
      { key: ',', shift: '<', code: 'Comma' },
      { key: '.', shift: '>', code: 'Period' },
      { key: '/', shift: '?', code: 'Slash' },
      { key: 'Shift', width: 'extra-wide', code: 'ShiftRight' }
    ],
    // Space row
    [
      { key: 'Ctrl', width: 'wide', code: 'ControlLeft' },
      { key: 'Alt', width: 'wide', code: 'AltLeft' },
      { key: ' ', width: 'space', code: 'Space', display: 'Space' },
      { key: 'Alt', width: 'wide', code: 'AltRight' },
      { key: 'Ctrl', width: 'wide', code: 'ControlRight' }
    ]
  ];

  // Handle physical keyboard events
  useEffect(() => {
    const handleKeyDown = (e) => {
      setPressedKey(e.code);
      if (e.key === 'Shift') {
        setShiftPressed(true);
      }
    };

    const handleKeyUp = (e) => {
      setPressedKey(null);
      if (e.key === 'Shift') {
        setShiftPressed(false);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, []);

  // Handle virtual key press
  const handleVirtualKeyPress = (keyData) => {
    if (disabledKeys.includes(keyData.key)) return;

    let keyToSend = keyData.key;

    // Handle special keys
    if (keyData.key === 'Shift') {
      setShiftPressed(!shiftPressed);
      return;
    }

    if (keyData.key === 'Backspace') {
      onKeyPress('Backspace');
      return;
    }

    if (keyData.key === 'Enter') {
      onKeyPress('Enter');
      return;
    }

    if (keyData.key === 'Tab') {
      onKeyPress('Tab');
      return;
    }

    if (keyData.key === ' ') {
      onKeyPress(' ');
      return;
    }

    // Handle shift combinations
    if (shiftPressed && keyData.shift) {
      keyToSend = keyData.shift;
    }

    // Send the key
    onKeyPress(keyToSend);

    // Visual feedback
    setPressedKey(keyData.code);
    setTimeout(() => setPressedKey(null), 150);
  };

  // Get the display character for a key
  const getDisplayChar = (keyData) => {
    if (keyData.display) return keyData.display;
    if (shiftPressed && keyData.shift) return keyData.shift;
    return keyData.key;
  };

  // Get key classes
  const getKeyClasses = (keyData) => {
    let classes = ['virtual-key'];
    
    if (keyData.width) {
      classes.push(`key-${keyData.width}`);
    }
    
    if (pressedKey === keyData.code) {
      classes.push('pressed');
    }
    
    if (highlightKey && (highlightKey === keyData.key || highlightKey === keyData.shift)) {
      classes.push('highlighted');
    }
    
    if (disabledKeys.includes(keyData.key)) {
      classes.push('disabled');
    }
    
    if (keyData.key === 'Shift' && shiftPressed) {
      classes.push('active');
    }

    // Special key styling
    if (['Shift', 'Ctrl', 'Alt', 'Tab', 'Caps', 'Enter', 'Backspace'].includes(keyData.key)) {
      classes.push('special-key');
    }

    return classes.join(' ');
  };

  if (!showKeyboard) return null;

  return (
    <div className="virtual-keyboard">
      <div className="keyboard-header">
        <h4>Virtual Keyboard</h4>
        <div className="keyboard-indicators">
          {shiftPressed && <span className="shift-indicator">⇧ Shift</span>}
        </div>
      </div>
      
      <div className="keyboard-container">
        {keyboardRows.map((row, rowIndex) => (
          <div key={rowIndex} className="keyboard-row">
            {row.map((keyData, keyIndex) => (
              <button
                key={`${rowIndex}-${keyIndex}`}
                className={getKeyClasses(keyData)}
                onClick={() => handleVirtualKeyPress(keyData)}
                disabled={disabledKeys.includes(keyData.key)}
              >
                <span className="key-main">{getDisplayChar(keyData)}</span>
                {keyData.shift && !shiftPressed && (
                  <span className="key-shift">{keyData.shift}</span>
                )}
              </button>
            ))}
          </div>
        ))}
      </div>
      
      <div className="keyboard-footer">
        <div className="keyboard-tips">
          <span>💡 Click keys or use your physical keyboard</span>
          {highlightKey && (
            <span className="highlight-tip">
              🎯 Next: <strong>{highlightKey}</strong>
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default VirtualKeyboard;
