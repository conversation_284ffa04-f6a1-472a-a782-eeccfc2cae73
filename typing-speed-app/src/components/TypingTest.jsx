import { useState, useEffect, useRef } from 'react';
import { testContent, getDifficultySettings } from '../data/testContent';
import { useTypingTest } from '../hooks/useTypingTest';
import './TypingTest.css';

const TypingTest = ({ difficulty, testType, onComplete, onBack }) => {
  const [testText, setTestText] = useState('');
  const settings = getDifficultySettings(difficulty);
  
  const {
    currentIndex,
    userInput,
    isActive,
    isCompleted,
    timeLeft,
    wpm,
    accuracy,
    handleKeyPress,
    resetTest,
    getResults,
    getCharacterStatus,
    inputRef
  } = useTypingTest(testText, settings);

  // Initialize test text
  useEffect(() => {
    const texts = testContent[difficulty][testType];
    const randomText = texts[Math.floor(Math.random() * texts.length)];
    setTestText(randomText);
  }, [difficulty, testType]);

  // Handle keyboard input
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (isCompleted) return;
      
      e.preventDefault();
      handleKeyPress(e.key);
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyPress, isCompleted]);

  // Handle test completion
  useEffect(() => {
    if (isCompleted) {
      const results = getResults();
      if (results) {
        setTimeout(() => onComplete(results), 1000);
      }
    }
  }, [isCompleted, getResults, onComplete]);

  // Focus input on mount
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getProgressPercentage = () => {
    return testText.length > 0 ? (userInput.length / testText.length) * 100 : 0;
  };

  return (
    <div className="typing-test fade-in">
      {/* Header with stats */}
      <div className="test-header">
        <button className="back-button" onClick={onBack}>
          ← Back to Selection
        </button>
        
        <div className="test-info">
          <span className="difficulty-badge">{difficulty}</span>
          <span className="test-type-badge">{testType}</span>
        </div>
      </div>

      {/* Stats Bar */}
      <div className="stats-bar">
        <div className="stat-item">
          <span className="stat-label">Time</span>
          <span className={`stat-value ${timeLeft <= 10 ? 'warning' : ''}`}>
            {formatTime(timeLeft)}
          </span>
        </div>
        
        <div className="stat-item">
          <span className="stat-label">WPM</span>
          <span className="stat-value">{wpm}</span>
        </div>
        
        <div className="stat-item">
          <span className="stat-label">Accuracy</span>
          <span className={`stat-value ${accuracy < 90 ? 'warning' : accuracy >= 95 ? 'good' : ''}`}>
            {accuracy}%
          </span>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="progress-container">
        <div className="progress-bar">
          <div 
            className="progress-fill" 
            style={{ width: `${getProgressPercentage()}%` }}
          ></div>
        </div>
        <span className="progress-text">
          {userInput.length} / {testText.length} characters
        </span>
      </div>

      {/* Typing Area */}
      <div className="typing-area">
        <div className="text-display">
          {testText.split('').map((char, index) => (
            <span
              key={index}
              className={`character ${getCharacterStatus(index)}`}
            >
              {char}
            </span>
          ))}
        </div>
        
        {/* Hidden input for mobile compatibility */}
        <input
          ref={inputRef}
          className="hidden-input"
          value={userInput}
          onChange={() => {}} // Controlled by keydown handler
          autoComplete="off"
          autoCorrect="off"
          autoCapitalize="off"
          spellCheck="false"
        />
      </div>

      {/* Instructions */}
      <div className="instructions">
        {!isActive && !isCompleted && (
          <p>Start typing to begin the test. Focus and accuracy are key!</p>
        )}
        {isActive && !isCompleted && (
          <p>Keep typing! {settings.allowBackspace ? 'Use backspace to correct mistakes.' : 'No backspace allowed - be careful!'}</p>
        )}
        {isCompleted && (
          <p className="completion-message">🎉 Test completed! Calculating results...</p>
        )}
      </div>

      {/* Reset Button */}
      {(isActive || isCompleted) && (
        <div className="test-actions">
          <button className="reset-button" onClick={resetTest}>
            🔄 Reset Test
          </button>
        </div>
      )}
    </div>
  );
};

export default TypingTest;
