import { useState, useEffect } from 'react';
import { useRandomGame } from '../hooks/useRandomGame';
import './RandomGame.css';

const RandomGame = ({ gameType, gameMode, difficulty, onComplete, onBack }) => {
  const {
    currentCharacter,
    nextCharacter,
    charactersTyped,
    correctCharacters,
    errors,
    isActive,
    isCompleted,
    timeLeft,
    streak,
    bestStreak,
    handleCharacterInput,
    resetGame,
    getResults,
    getCurrentStats,
    getCharacterInfo,
    hasTimeLimit,
    hasTargetCount,
    targetCount
  } = useRandomGame(gameType, gameMode, difficulty);

  const [showInstructions, setShowInstructions] = useState(true);
  const stats = getCurrentStats();
  const currentCharInfo = getCharacterInfo(currentCharacter);
  const nextCharInfo = getCharacterInfo(nextCharacter);

  // Handle game completion
  useEffect(() => {
    if (isCompleted) {
      const results = getResults();
      if (results) {
        setTimeout(() => onComplete(results), 1500);
      }
    }
  }, [isCompleted, getResults, onComplete]);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getCharacterClass = (charInfo) => {
    let classes = ['character-display'];
    if (charInfo.isSymbol) classes.push('symbol');
    if (charInfo.isNumber) classes.push('number');
    if (charInfo.isLetter) classes.push('letter');
    if (charInfo.isUppercase) classes.push('uppercase');
    if (charInfo.isLowercase) classes.push('lowercase');
    return classes.join(' ');
  };

  const handleStartGame = () => {
    setShowInstructions(false);
  };

  if (showInstructions) {
    return (
      <div className="random-game fade-in">
        <div className="game-instructions">
          <div className="instruction-header">
            <span className="mode-icon">{gameType.icon}</span>
            <h2>{gameType.name}</h2>
            <p>{gameType.description}</p>
          </div>
          
          <div className="instruction-content">
            <div className="instruction-item">
              <span className="instruction-icon">⌨️</span>
              <div>
                <h4>How to Play</h4>
                <p>Type the character shown on screen as quickly and accurately as possible.</p>
              </div>
            </div>
            
            <div className="instruction-item">
              <span className="instruction-icon">🎯</span>
              <div>
                <h4>Game Mode: {gameMode.name}</h4>
                <p>{gameMode.description}</p>
              </div>
            </div>
            
            <div className="instruction-item">
              <span className="instruction-icon">⚡</span>
              <div>
                <h4>Difficulty: {difficulty.name}</h4>
                <p>{difficulty.description}</p>
              </div>
            </div>
            
            {hasTargetCount && (
              <div className="instruction-item">
                <span className="instruction-icon">🎯</span>
                <div>
                  <h4>Target</h4>
                  <p>Type {targetCount} characters correctly to complete the challenge.</p>
                </div>
              </div>
            )}
            
            {hasTimeLimit && (
              <div className="instruction-item">
                <span className="instruction-icon">⏰</span>
                <div>
                  <h4>Time Limit</h4>
                  <p>You have {formatTime(gameMode.timeLimit)} to type as many characters as possible.</p>
                </div>
              </div>
            )}
          </div>
          
          <div className="instruction-actions">
            <button className="back-button" onClick={onBack}>
              ← Back to Menu
            </button>
            <button className="start-game-button" onClick={handleStartGame}>
              Start Game 🚀
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="random-game fade-in">
      {/* Header */}
      <div className="game-header">
        <button className="back-button" onClick={onBack}>
          ← Back
        </button>
        
        <div className="game-info">
          <span className="mode-badge">
            {gameType.icon} {gameType.name} - {gameMode.name}
          </span>
        </div>
      </div>

      {/* Stats Bar */}
      <div className="game-stats">
        <div className="stat-item">
          <span className="stat-label">Typed</span>
          <span className="stat-value">{correctCharacters}</span>
          {hasTargetCount && <span className="stat-target">/{targetCount}</span>}
        </div>
        
        <div className="stat-item">
          <span className="stat-label">Accuracy</span>
          <span className={`stat-value ${stats.accuracy < 90 ? 'warning' : stats.accuracy >= 95 ? 'good' : ''}`}>
            {stats.accuracy}%
          </span>
        </div>
        
        <div className="stat-item">
          <span className="stat-label">Speed</span>
          <span className="stat-value">{stats.charactersPerSecond}/s</span>
        </div>
        
        <div className="stat-item">
          <span className="stat-label">Streak</span>
          <span className={`stat-value ${streak > 10 ? 'streak-hot' : ''}`}>{streak}</span>
        </div>
        
        <div className="stat-item">
          <span className="stat-label">Errors</span>
          <span className={`stat-value ${errors > 0 ? 'has-errors' : ''}`}>{errors}</span>
        </div>
        
        {hasTimeLimit && (
          <div className="stat-item">
            <span className="stat-label">Time</span>
            <span className={`stat-value ${timeLeft <= 10 ? 'time-warning' : ''}`}>
              {formatTime(timeLeft)}
            </span>
          </div>
        )}
        
        {!hasTimeLimit && isActive && (
          <div className="stat-item">
            <span className="stat-label">Time</span>
            <span className="stat-value">{stats.elapsed}s</span>
          </div>
        )}
      </div>

      {/* Progress Bar */}
      {hasTargetCount && (
        <div className="progress-container">
          <div className="progress-bar">
            <div 
              className="progress-fill" 
              style={{ width: `${stats.progress}%` }}
            ></div>
          </div>
          <span className="progress-text">
            {Math.round(stats.progress)}% Complete ({correctCharacters}/{targetCount})
          </span>
        </div>
      )}

      {/* Character Display */}
      <div className="character-area">
        <div className="current-character-section">
          <div className="character-label">Type This Character:</div>
          <div className={`${getCharacterClass(currentCharInfo)} current`}>
            <span className="char">{currentCharacter}</span>
            <span className="char-name">{currentCharInfo.displayName}</span>
          </div>
        </div>
        
        {difficulty.showNext && nextCharacter && (
          <div className="next-character-section">
            <div className="character-label">Next:</div>
            <div className={`${getCharacterClass(nextCharInfo)} next`}>
              <span className="char">{nextCharacter}</span>
              <span className="char-name">{nextCharInfo.displayName}</span>
            </div>
          </div>
        )}
      </div>

      {/* Character Set Preview */}
      <div className="character-set-preview">
        <h4>Character Set:</h4>
        <div className="character-grid">
          {gameType.characterSet.slice(0, 20).map((char, index) => (
            <span key={index} className="preview-char">{char}</span>
          ))}
          {gameType.characterSet.length > 20 && (
            <span className="more-chars">+{gameType.characterSet.length - 20} more</span>
          )}
        </div>
      </div>

      {/* Game Status */}
      <div className="game-status">
        {!isActive && !isCompleted && (
          <p className="status-message">Press any key to start typing!</p>
        )}
        
        {isActive && !isCompleted && (
          <p className="status-message">
            Keep going! {streak > 0 && `🔥 ${streak} character streak!`}
          </p>
        )}
        
        {isCompleted && (
          <p className="completion-message">
            🎉 {hasTargetCount && stats.progress >= 100 
              ? 'Target reached! Excellent work!' 
              : 'Time\'s up! Great effort!'}
          </p>
        )}
      </div>

      {/* Action Buttons */}
      <div className="game-actions">
        <button className="reset-button" onClick={resetGame}>
          🔄 Reset Game
        </button>
        
        {isCompleted && (
          <button className="continue-button" onClick={() => onComplete(getResults())}>
            View Results →
          </button>
        )}
      </div>
    </div>
  );
};

export default RandomGame;
