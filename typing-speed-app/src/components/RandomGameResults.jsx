import './RandomGameResults.css';

const RandomGameResults = ({ results, onRetakeGame, onNewGame }) => {
  if (!results) return null;

  const getPerformanceLevel = (charactersPerSecond, accuracy, isTargetReached) => {
    if (accuracy < 85) return { level: 'Practice More', color: 'error', icon: '📚' };
    if (!isTargetReached && results.gameMode !== 'Timed Challenge') {
      return { level: 'Incomplete', color: 'warning', icon: '⏱️' };
    }
    
    if (charactersPerSecond < 1) return { level: 'Beginner', color: 'warning', icon: '🌱' };
    if (charactersPerSecond < 2) return { level: 'Good', color: 'primary', icon: '💪' };
    if (charactersPerSecond < 3) return { level: 'Excellent', color: 'success', icon: '🚀' };
    return { level: 'Master', color: 'success', icon: '👑' };
  };

  const getSpeedGrade = (charactersPerSecond) => {
    if (charactersPerSecond >= 4) return { grade: 'A+', color: 'success' };
    if (charactersPerSecond >= 3) return { grade: 'A', color: 'success' };
    if (charactersPerSecond >= 2.5) return { grade: 'B+', color: 'primary' };
    if (charactersPerSecond >= 2) return { grade: 'B', color: 'primary' };
    if (charactersPerSecond >= 1.5) return { grade: 'C+', color: 'warning' };
    if (charactersPerSecond >= 1) return { grade: 'C', color: 'warning' };
    return { grade: 'D', color: 'error' };
  };

  const performance = getPerformanceLevel(results.charactersPerSecond, results.accuracy, results.isTargetReached);
  const speedGrade = getSpeedGrade(results.charactersPerSecond);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.round(seconds % 60);
    return mins > 0 ? `${mins}:${secs.toString().padStart(2, '0')}` : `${secs}s`;
  };

  return (
    <div className="random-results fade-in">
      <div className="results-header">
        <div className="performance-badge">
          <span className="performance-icon">{performance.icon}</span>
          <div className="performance-text">
            <h2>Challenge Complete!</h2>
            <p className={`performance-level ${performance.color}`}>
              {performance.level}
            </p>
          </div>
        </div>
      </div>

      <div className="results-grid">
        {/* Main Stats */}
        <div className="stats-section">
          <h3>Performance Metrics</h3>
          <div className="main-stats">
            <div className="stat-card primary">
              <div className="stat-icon">📊</div>
              <div className="stat-content">
                <span className="stat-value">{results.correctCharacters}</span>
                <span className="stat-label">Characters Typed</span>
                {results.isTargetReached && (
                  <span className="completion-badge">🎉 Target Reached!</span>
                )}
              </div>
            </div>

            <div className="stat-card success">
              <div className="stat-icon">⚡</div>
              <div className="stat-content">
                <span className="stat-value">{results.charactersPerSecond}</span>
                <span className="stat-label">Characters Per Second</span>
                <span className={`speed-grade ${speedGrade.color}`}>
                  Grade: {speedGrade.grade}
                </span>
              </div>
            </div>

            <div className="stat-card secondary">
              <div className="stat-icon">🎯</div>
              <div className="stat-content">
                <span className="stat-value">{results.accuracy}%</span>
                <span className="stat-label">Accuracy</span>
                <span className="stat-note">
                  {results.errors === 0 ? 'Perfect!' : `${results.errors} errors`}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Detailed Stats */}
        <div className="details-section">
          <h3>Game Details</h3>
          <div className="detail-stats">
            <div className="detail-item">
              <span className="detail-label">⏱️ Total Time</span>
              <span className="detail-value">{formatTime(results.totalTime)}</span>
            </div>
            
            <div className="detail-item">
              <span className="detail-label">🎮 Game Type</span>
              <span className="detail-value">{results.gameType}</span>
            </div>
            
            <div className="detail-item">
              <span className="detail-label">🎯 Game Mode</span>
              <span className="detail-value">{results.gameMode}</span>
            </div>
            
            <div className="detail-item">
              <span className="detail-label">⚡ Difficulty</span>
              <span className="detail-value">{results.difficulty}</span>
            </div>
            
            <div className="detail-item">
              <span className="detail-label">🔥 Best Streak</span>
              <span className="detail-value">{results.bestStreak} chars</span>
            </div>
            
            <div className="detail-item">
              <span className="detail-label">⚡ Avg Char Time</span>
              <span className="detail-value">{results.averageCharacterTime}ms</span>
            </div>
            
            <div className="detail-item">
              <span className="detail-label">📝 Total Typed</span>
              <span className="detail-value">{results.charactersTyped}</span>
            </div>
            
            <div className="detail-item">
              <span className="detail-label">❌ Total Errors</span>
              <span className="detail-value">{results.errors}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Visualization */}
      <div className="progress-section">
        <h3>Performance Breakdown</h3>
        <div className="progress-bars">
          <div className="progress-item">
            <div className="progress-header">
              <span>Speed (chars/sec)</span>
              <span>{results.charactersPerSecond}/5</span>
            </div>
            <div className="progress-bar">
              <div 
                className="progress-fill speed" 
                style={{ width: `${Math.min(results.charactersPerSecond * 20, 100)}%` }}
              ></div>
            </div>
          </div>
          
          <div className="progress-item">
            <div className="progress-header">
              <span>Accuracy</span>
              <span>{results.accuracy}%</span>
            </div>
            <div className="progress-bar">
              <div 
                className="progress-fill accuracy" 
                style={{ width: `${results.accuracy}%` }}
              ></div>
            </div>
          </div>
          
          <div className="progress-item">
            <div className="progress-header">
              <span>Consistency</span>
              <span>{Math.max(0, 100 - results.errors * 3)}%</span>
            </div>
            <div className="progress-bar">
              <div 
                className="progress-fill consistency" 
                style={{ width: `${Math.max(0, 100 - results.errors * 3)}%` }}
              ></div>
            </div>
          </div>
          
          <div className="progress-item">
            <div className="progress-header">
              <span>Streak Performance</span>
              <span>{Math.min(results.bestStreak * 5, 100)}%</span>
            </div>
            <div className="progress-bar">
              <div 
                className="progress-fill streak" 
                style={{ width: `${Math.min(results.bestStreak * 5, 100)}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="results-actions">
        <button className="action-button secondary" onClick={onRetakeGame}>
          🔄 Play Again
        </button>
        <button className="action-button primary" onClick={onNewGame}>
          🎮 Try Different Game
        </button>
      </div>

      {/* Tips Section */}
      <div className="tips-section">
        <h3>💡 Tips for Improvement</h3>
        <div className="tips-grid">
          {results.accuracy < 90 && (
            <div className="tip-card">
              <span className="tip-icon">🎯</span>
              <p>Focus on accuracy over speed. Take time to identify each character before typing.</p>
            </div>
          )}
          {results.charactersPerSecond < 2 && (
            <div className="tip-card">
              <span className="tip-icon">⚡</span>
              <p>Practice typing without looking at the keyboard. Build muscle memory for character positions.</p>
            </div>
          )}
          {results.errors > 5 && (
            <div className="tip-card">
              <span className="tip-icon">🔍</span>
              <p>Slow down and focus on precision. Speed will come naturally with practice.</p>
            </div>
          )}
          {results.bestStreak < 10 && (
            <div className="tip-card">
              <span className="tip-icon">🔥</span>
              <p>Build longer streaks by staying focused and maintaining a steady rhythm.</p>
            </div>
          )}
          {results.gameType.includes('Symbol') && (
            <div className="tip-card">
              <span className="tip-icon">⚡</span>
              <p>For symbols, learn the shift key combinations and practice them regularly.</p>
            </div>
          )}
          {results.gameType.includes('Mixed') && (
            <div className="tip-card">
              <span className="tip-icon">🎲</span>
              <p>Mixed character sets are challenging! Practice each type separately, then combine.</p>
            </div>
          )}
          <div className="tip-card">
            <span className="tip-icon">💪</span>
            <p>Regular practice with different character sets improves overall typing versatility!</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RandomGameResults;
