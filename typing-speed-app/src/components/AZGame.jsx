import { useState, useEffect } from 'react';
import { useAZGame } from '../hooks/useAZGame';
import './AZGame.css';

const AZGame = ({ gameMode = 'normal', onComplete, onBack }) => {
  const {
    currentLetter,
    currentLetterIndex,
    isActive,
    isCompleted,
    timeLeft,
    errors,
    streak,
    bestStreak,
    alphabet,
    handleLetterInput,
    resetGame,
    getResults,
    getLetterStatus,
    getProgress,
    getCurrentStats,
    isTimedMode
  } = useAZGame(gameMode);

  const [showInstructions, setShowInstructions] = useState(true);
  const stats = getCurrentStats();

  // Handle game completion
  useEffect(() => {
    if (isCompleted) {
      const results = getResults();
      if (results) {
        setTimeout(() => onComplete(results), 1500);
      }
    }
  }, [isCompleted, getResults, onComplete]);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getGameModeInfo = () => {
    const modes = {
      normal: {
        title: 'Classic A-Z Challenge',
        description: 'Type all letters from A to Z as fast as possible',
        icon: '🔤'
      },
      timed: {
        title: 'Timed A-Z Challenge',
        description: 'Type as many letters as possible in 60 seconds',
        icon: '⏱️'
      },
      speed: {
        title: 'Speed A-Z Challenge',
        description: 'Complete the alphabet in record time',
        icon: '⚡'
      }
    };
    return modes[gameMode] || modes.normal;
  };

  const modeInfo = getGameModeInfo();

  const handleStartGame = () => {
    setShowInstructions(false);
  };

  if (showInstructions) {
    return (
      <div className="az-game fade-in">
        <div className="game-instructions">
          <div className="instruction-header">
            <span className="mode-icon">{modeInfo.icon}</span>
            <h2>{modeInfo.title}</h2>
            <p>{modeInfo.description}</p>
          </div>
          
          <div className="instruction-content">
            <div className="instruction-item">
              <span className="instruction-icon">⌨️</span>
              <div>
                <h4>How to Play</h4>
                <p>Type the letters A through Z in order. You can use your keyboard or click the buttons.</p>
              </div>
            </div>
            
            <div className="instruction-item">
              <span className="instruction-icon">🎯</span>
              <div>
                <h4>Scoring</h4>
                <p>Build streaks by typing correctly. Mistakes will reset your streak but you can continue.</p>
              </div>
            </div>
            
            {isTimedMode && (
              <div className="instruction-item">
                <span className="instruction-icon">⏰</span>
                <div>
                  <h4>Time Limit</h4>
                  <p>You have 60 seconds to type as many letters as possible in sequence.</p>
                </div>
              </div>
            )}
          </div>
          
          <div className="instruction-actions">
            <button className="back-button" onClick={onBack}>
              ← Back to Menu
            </button>
            <button className="start-game-button" onClick={handleStartGame}>
              Start Game 🚀
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="az-game fade-in">
      {/* Header */}
      <div className="game-header">
        <button className="back-button" onClick={onBack}>
          ← Back
        </button>
        
        <div className="game-info">
          <span className="mode-badge">{modeInfo.icon} {modeInfo.title}</span>
        </div>
      </div>

      {/* Stats Bar */}
      <div className="game-stats">
        <div className="stat-item">
          <span className="stat-label">Current Letter</span>
          <span className="stat-value current-letter-display">{currentLetter || 'A'}</span>
        </div>
        
        <div className="stat-item">
          <span className="stat-label">Progress</span>
          <span className="stat-value">{currentLetterIndex}/26</span>
        </div>
        
        <div className="stat-item">
          <span className="stat-label">Streak</span>
          <span className={`stat-value ${streak > 5 ? 'streak-hot' : ''}`}>{streak}</span>
        </div>
        
        <div className="stat-item">
          <span className="stat-label">Errors</span>
          <span className={`stat-value ${errors > 0 ? 'has-errors' : ''}`}>{errors}</span>
        </div>
        
        {isTimedMode && (
          <div className="stat-item">
            <span className="stat-label">Time</span>
            <span className={`stat-value ${timeLeft <= 10 ? 'time-warning' : ''}`}>
              {formatTime(timeLeft)}
            </span>
          </div>
        )}
        
        {!isTimedMode && isActive && (
          <div className="stat-item">
            <span className="stat-label">Time</span>
            <span className="stat-value">{stats.elapsed}s</span>
          </div>
        )}
      </div>

      {/* Progress Bar */}
      <div className="progress-container">
        <div className="progress-bar">
          <div 
            className="progress-fill" 
            style={{ width: `${getProgress()}%` }}
          ></div>
        </div>
        <span className="progress-text">
          {Math.round(getProgress())}% Complete
        </span>
      </div>

      {/* Letter Grid */}
      <div className="letter-grid">
        {alphabet.map((letter, index) => (
          <button
            key={letter}
            className={`letter-button ${getLetterStatus(letter)}`}
            onClick={() => handleLetterInput(letter)}
            disabled={getLetterStatus(letter) === 'completed'}
          >
            {letter}
          </button>
        ))}
      </div>

      {/* Current Letter Focus */}
      <div className="current-focus">
        <div className="focus-content">
          <span className="focus-label">Next Letter:</span>
          <span className="focus-letter">{currentLetter}</span>
          <span className="focus-hint">
            {isActive ? 'Type it or click it!' : 'Press any key to start'}
          </span>
        </div>
      </div>

      {/* Game Status */}
      <div className="game-status">
        {!isActive && !isCompleted && (
          <p className="status-message">Press any letter key to start the challenge!</p>
        )}
        
        {isActive && !isCompleted && (
          <p className="status-message">
            Keep going! {streak > 0 && `🔥 ${streak} letter streak!`}
          </p>
        )}
        
        {isCompleted && (
          <p className="completion-message">
            🎉 {currentLetterIndex === 26 ? 'Perfect! You completed the alphabet!' : 'Time\'s up! Great effort!'}
          </p>
        )}
      </div>

      {/* Action Buttons */}
      <div className="game-actions">
        <button className="reset-button" onClick={resetGame}>
          🔄 Reset Game
        </button>
        
        {isCompleted && (
          <button className="continue-button" onClick={() => onComplete(getResults())}>
            View Results →
          </button>
        )}
      </div>
    </div>
  );
};

export default AZGame;
