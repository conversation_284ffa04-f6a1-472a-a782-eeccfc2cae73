.header {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  border-bottom: 1px solid var(--border-primary);
  padding: 1.5rem 2rem;
  box-shadow: var(--shadow-md);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.logo-icon {
  font-size: 2.5rem;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: pulse 2s ease-in-out infinite;
}

.logo-text {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--text-accent) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
}

.header-subtitle {
  color: var(--text-secondary);
  font-size: 1.1rem;
  font-weight: 400;
  opacity: 0.9;
}

@media (max-width: 768px) {
  .header {
    padding: 1rem;
  }
  
  .logo {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .logo-icon {
    font-size: 2rem;
  }
  
  .logo-text {
    font-size: 2rem;
  }
  
  .header-subtitle {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .logo-text {
    font-size: 1.75rem;
  }
  
  .header-subtitle {
    font-size: 0.9rem;
  }
}
