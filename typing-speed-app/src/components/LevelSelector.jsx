import { useState } from 'react';
import { getDifficultySettings, getTestTypeSettings } from '../data/testContent';
import './LevelSelector.css';

const LevelSelector = ({ onStartTest }) => {
  const [selectedDifficulty, setSelectedDifficulty] = useState('easy');
  const [selectedTestType, setSelectedTestType] = useState('basic');

  const difficulties = ['easy', 'medium', 'hard'];
  const testTypes = ['basic', 'intermediate', 'advanced'];

  const handleStartTest = () => {
    onStartTest(selectedDifficulty, selectedTestType);
  };

  const getDifficultyIcon = (difficulty) => {
    const icons = {
      easy: '🌱',
      medium: '🔥',
      hard: '⚡'
    };
    return icons[difficulty];
  };

  const getTestTypeIcon = (testType) => {
    const icons = {
      basic: '📝',
      intermediate: '💼',
      advanced: '🎓'
    };
    return icons[testType];
  };

  return (
    <div className="level-selector fade-in">
      <div className="selector-header">
        <h2>Choose Your Challenge</h2>
        <p>Select difficulty and test type to begin your typing journey</p>
      </div>

      <div className="selection-grid">
        {/* Difficulty Selection */}
        <div className="selection-section">
          <h3>Difficulty Level</h3>
          <div className="options-grid">
            {difficulties.map((difficulty) => {
              const settings = getDifficultySettings(difficulty);
              return (
                <button
                  key={difficulty}
                  className={`option-card ${selectedDifficulty === difficulty ? 'selected' : ''}`}
                  onClick={() => setSelectedDifficulty(difficulty)}
                >
                  <div className="option-icon">{getDifficultyIcon(difficulty)}</div>
                  <div className="option-title">{difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}</div>
                  <div className="option-description">{settings.description}</div>
                  <div className="option-details">
                    <span>⏱️ {settings.timeLimit}s</span>
                    <span>📄 {settings.wordsToShow} words</span>
                  </div>
                </button>
              );
            })}
          </div>
        </div>

        {/* Test Type Selection */}
        <div className="selection-section">
          <h3>Test Type</h3>
          <div className="options-grid">
            {testTypes.map((testType) => {
              const settings = getTestTypeSettings(testType);
              return (
                <button
                  key={testType}
                  className={`option-card ${selectedTestType === testType ? 'selected' : ''}`}
                  onClick={() => setSelectedTestType(testType)}
                >
                  <div className="option-icon">{getTestTypeIcon(testType)}</div>
                  <div className="option-title">{testType.charAt(0).toUpperCase() + testType.slice(1)}</div>
                  <div className="option-description">{settings.description}</div>
                  <div className="option-focus">{settings.focusArea}</div>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      <div className="start-section">
        <div className="selected-summary">
          <h4>Selected Configuration:</h4>
          <div className="summary-details">
            <span className="summary-item">
              {getDifficultyIcon(selectedDifficulty)} {selectedDifficulty.charAt(0).toUpperCase() + selectedDifficulty.slice(1)} Difficulty
            </span>
            <span className="summary-item">
              {getTestTypeIcon(selectedTestType)} {selectedTestType.charAt(0).toUpperCase() + selectedTestType.slice(1)} Test
            </span>
          </div>
        </div>
        
        <button className="start-button" onClick={handleStartTest}>
          <span>Start Typing Test</span>
          <span className="start-icon">🚀</span>
        </button>
      </div>
    </div>
  );
};

export default LevelSelector;
