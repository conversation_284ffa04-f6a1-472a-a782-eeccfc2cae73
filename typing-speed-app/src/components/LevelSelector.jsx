import { useState } from 'react';
import { getDifficultySettings, getTestTypeSettings } from '../data/testContent';
import { gameTypes, gameModes, difficultySettings, getGameTypesByCategory } from '../data/characterSets';
import './LevelSelector.css';

const LevelSelector = ({ onStartTest, onStartAZGame, onStartRandomGame }) => {
  const [selectedDifficulty, setSelectedDifficulty] = useState('easy');
  const [selectedTestType, setSelectedTestType] = useState('basic');
  const [selectedMode, setSelectedMode] = useState('typing'); // 'typing', 'azgame', or 'random'
  const [selectedRandomGameType, setSelectedRandomGameType] = useState('randomLettersLower');
  const [selectedGameMode, setSelectedGameMode] = useState('practice');
  const [showSettings, setShowSettings] = useState(false);
  const [keyboardEnabled, setKeyboardEnabled] = useState(true);

  const difficulties = ['easy', 'medium', 'hard'];
  const testTypes = ['basic', 'intermediate', 'advanced'];
  const azGameModes = ['normal', 'timed', 'speed'];

  const handleStartTest = () => {
    if (selectedMode === 'typing') {
      onStartTest(selectedDifficulty, selectedTestType);
    } else if (selectedMode === 'azgame') {
      onStartAZGame(selectedTestType); // Use testType as gameMode for A-Z game
    } else if (selectedMode === 'random') {
      onStartRandomGame(selectedRandomGameType, selectedGameMode, selectedDifficulty);
    }
  };

  const getDifficultyIcon = (difficulty) => {
    const icons = {
      easy: '🌱',
      medium: '🔥',
      hard: '⚡'
    };
    return icons[difficulty];
  };

  const getDifficultyColor = (difficulty) => {
    const colors = {
      easy: 'success',
      medium: 'warning',
      hard: 'error'
    };
    return colors[difficulty];
  };

  const getDifficultyStars = (difficulty) => {
    const stars = {
      easy: '★☆☆',
      medium: '★★☆',
      hard: '★★★'
    };
    return stars[difficulty];
  };

  const getTestTypeIcon = (testType) => {
    if (selectedMode === 'azgame') {
      const icons = {
        basic: '🔤',
        intermediate: '⏱️',
        advanced: '⚡'
      };
      return icons[testType];
    }

    const icons = {
      basic: '📝',
      intermediate: '💼',
      advanced: '🎓'
    };
    return icons[testType];
  };

  const getAZGameModeSettings = (mode) => {
    const settings = {
      basic: {
        gameMode: 'normal',
        description: 'Classic A-Z Challenge',
        focusArea: 'Type all letters A to Z in order'
      },
      intermediate: {
        gameMode: 'timed',
        description: 'Timed A-Z Challenge',
        focusArea: 'Type as many letters as possible in 60 seconds'
      },
      advanced: {
        gameMode: 'speed',
        description: 'Speed A-Z Challenge',
        focusArea: 'Complete the alphabet in record time'
      }
    };
    return settings[mode] || settings.basic;
  };

  return (
    <div className="level-selector fade-in">
      <div className="selector-header">
        <div className="header-content">
          <h2>Choose Your Challenge</h2>
          <p>Select your preferred typing challenge to begin your journey</p>
        </div>
        <button
          className="settings-button"
          onClick={() => setShowSettings(!showSettings)}
        >
          ⚙️ Settings
        </button>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="settings-panel">
          <h3>⚙️ Game Settings</h3>
          <div className="settings-options">
            <div className="setting-item">
              <label className="setting-label">
                <input
                  type="checkbox"
                  checked={keyboardEnabled}
                  onChange={(e) => setKeyboardEnabled(e.target.checked)}
                />
                <span className="checkmark"></span>
                Enable Virtual Keyboard
              </label>
              <p className="setting-description">
                Show an interactive on-screen keyboard during games
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Mode Selection */}
      <div className="mode-selection">
        <h3>Challenge Type</h3>
        <div className="mode-options">
          <button
            className={`mode-card ${selectedMode === 'typing' ? 'selected' : ''}`}
            onClick={() => setSelectedMode('typing')}
          >
            <div className="mode-icon">⌨️</div>
            <div className="mode-title">Typing Test</div>
            <div className="mode-description">Traditional typing speed and accuracy test</div>
          </button>

          <button
            className={`mode-card ${selectedMode === 'azgame' ? 'selected' : ''}`}
            onClick={() => setSelectedMode('azgame')}
          >
            <div className="mode-icon">🎮</div>
            <div className="mode-title">A-Z Letter Game</div>
            <div className="mode-description">Sequential alphabet typing challenge</div>
          </button>

          <button
            className={`mode-card ${selectedMode === 'random' ? 'selected' : ''}`}
            onClick={() => setSelectedMode('random')}
          >
            <div className="mode-icon">🎲</div>
            <div className="mode-title">Random Character Game</div>
            <div className="mode-description">Random letters, numbers, symbols & mixed challenges</div>
          </button>
        </div>
      </div>

      <div className="selection-grid">
        {/* Difficulty Selection - For Typing Test and Random Game */}
        {(selectedMode === 'typing' || selectedMode === 'random') && (
          <div className="selection-section">
            <h3>Difficulty Level</h3>
            <div className="options-grid">
              {difficulties.map((difficulty) => {
                const settings = selectedMode === 'typing'
                  ? getDifficultySettings(difficulty)
                  : difficultySettings[difficulty];
                return (
                  <button
                    key={difficulty}
                    className={`option-card difficulty-${getDifficultyColor(difficulty)} ${selectedDifficulty === difficulty ? 'selected' : ''}`}
                    onClick={() => setSelectedDifficulty(difficulty)}
                  >
                    <div className="option-header">
                      <div className="option-icon">{getDifficultyIcon(difficulty)}</div>
                      <div className="difficulty-stars">{getDifficultyStars(difficulty)}</div>
                    </div>
                    <div className="option-title">{difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}</div>
                    <div className="option-description">{settings.description}</div>
                    {selectedMode === 'typing' && (
                      <div className="option-details">
                        <span>⏱️ {settings.timeLimit}s</span>
                        <span>📄 {settings.wordsToShow} words</span>
                      </div>
                    )}
                  </button>
                );
              })}
            </div>
          </div>
        )}

        {/* Random Game Type Selection */}
        {selectedMode === 'random' && (
          <div className="selection-section">
            <h3>Character Type</h3>
            <div className="options-grid random-game-grid">
              {Object.entries(gameTypes).map(([key, gameType]) => (
                <button
                  key={key}
                  className={`option-card ${selectedRandomGameType === key ? 'selected' : ''}`}
                  onClick={() => setSelectedRandomGameType(key)}
                >
                  <div className="option-icon">{gameType.icon}</div>
                  <div className="option-title">{gameType.name}</div>
                  <div className="option-description">{gameType.description}</div>
                  <div className="option-details">
                    <span>📝 {gameType.characterSet.length} chars</span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Random Game Mode Selection */}
        {selectedMode === 'random' && (
          <div className="selection-section">
            <h3>Game Mode</h3>
            <div className="options-grid">
              {Object.entries(gameModes).map(([key, mode]) => (
                <button
                  key={key}
                  className={`option-card ${selectedGameMode === key ? 'selected' : ''}`}
                  onClick={() => setSelectedGameMode(key)}
                >
                  <div className="option-icon">{mode.icon}</div>
                  <div className="option-title">{mode.name}</div>
                  <div className="option-description">{mode.description}</div>
                  <div className="option-details">
                    {mode.timeLimit && <span>⏱️ {mode.timeLimit}s</span>}
                    {mode.targetCount && <span>🎯 {mode.targetCount} chars</span>}
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Test Type Selection - For Typing Test and A-Z Game */}
        {(selectedMode === 'typing' || selectedMode === 'azgame') && (
          <div className="selection-section">
            <h3>{selectedMode === 'typing' ? 'Test Type' : 'Game Mode'}</h3>
            <div className="options-grid">
              {testTypes.map((testType) => {
                const settings = selectedMode === 'typing'
                  ? getTestTypeSettings(testType)
                  : getAZGameModeSettings(testType);

                return (
                  <button
                    key={testType}
                    className={`option-card ${selectedTestType === testType ? 'selected' : ''}`}
                    onClick={() => setSelectedTestType(testType)}
                  >
                    <div className="option-icon">{getTestTypeIcon(testType)}</div>
                    <div className="option-title">
                      {selectedMode === 'typing'
                        ? testType.charAt(0).toUpperCase() + testType.slice(1)
                        : testType === 'basic' ? 'Normal'
                        : testType === 'intermediate' ? 'Timed'
                        : 'Speed'
                      }
                    </div>
                    <div className="option-description">{settings.description}</div>
                    <div className="option-focus">{settings.focusArea}</div>
                  </button>
                );
              })}
            </div>
          </div>
        )}
      </div>

      <div className="start-section">
        <div className="selected-summary">
          <h4>Selected Configuration:</h4>
          <div className="summary-details">
            {(selectedMode === 'typing' || selectedMode === 'random') && (
              <span className="summary-item">
                {getDifficultyIcon(selectedDifficulty)} {selectedDifficulty.charAt(0).toUpperCase() + selectedDifficulty.slice(1)} Difficulty
              </span>
            )}

            {selectedMode === 'typing' && (
              <span className="summary-item">
                {getTestTypeIcon(selectedTestType)} {selectedTestType.charAt(0).toUpperCase() + selectedTestType.slice(1)} Test
              </span>
            )}

            {selectedMode === 'azgame' && (
              <span className="summary-item">
                {getTestTypeIcon(selectedTestType)} {
                  selectedTestType === 'basic' ? 'Normal A-Z Game'
                  : selectedTestType === 'intermediate' ? 'Timed A-Z Game'
                  : 'Speed A-Z Game'
                }
              </span>
            )}

            {selectedMode === 'random' && (
              <>
                <span className="summary-item">
                  {gameTypes[selectedRandomGameType]?.icon} {gameTypes[selectedRandomGameType]?.name}
                </span>
                <span className="summary-item">
                  {gameModes[selectedGameMode]?.icon} {gameModes[selectedGameMode]?.name}
                </span>
              </>
            )}
          </div>
        </div>

        <button className="start-button" onClick={handleStartTest}>
          <span>
            {selectedMode === 'typing' ? 'Start Typing Test'
             : selectedMode === 'azgame' ? 'Start A-Z Game'
             : 'Start Random Game'}
          </span>
          <span className="start-icon">
            {selectedMode === 'typing' ? '🚀'
             : selectedMode === 'azgame' ? '🎮'
             : '🎲'}
          </span>
        </button>
      </div>
    </div>
  );
};

export default LevelSelector;
