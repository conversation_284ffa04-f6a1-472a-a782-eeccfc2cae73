import './AZGameResults.css';

const AZGameResults = ({ results, onRetakeGame, onNewGame }) => {
  if (!results) return null;

  const getPerformanceLevel = (lettersPerSecond, accuracy, isFullCompletion) => {
    if (!isFullCompletion) {
      return { level: 'Incomplete', color: 'warning', icon: '⏱️' };
    }
    
    if (accuracy < 85) return { level: 'Practice More', color: 'error', icon: '📚' };
    if (lettersPerSecond < 1) return { level: 'Beginner', color: 'warning', icon: '🌱' };
    if (lettersPerSecond < 2) return { level: 'Good', color: 'primary', icon: '💪' };
    if (lettersPerSecond < 3) return { level: 'Excellent', color: 'success', icon: '🚀' };
    return { level: 'Master', color: 'success', icon: '👑' };
  };

  const getSpeedGrade = (lettersPerSecond) => {
    if (lettersPerSecond >= 3) return { grade: 'A+', color: 'success' };
    if (lettersPerSecond >= 2.5) return { grade: 'A', color: 'success' };
    if (lettersPerSecond >= 2) return { grade: 'B+', color: 'primary' };
    if (lettersPerSecond >= 1.5) return { grade: 'B', color: 'primary' };
    if (lettersPerSecond >= 1) return { grade: 'C+', color: 'warning' };
    if (lettersPerSecond >= 0.5) return { grade: 'C', color: 'warning' };
    return { grade: 'D', color: 'error' };
  };

  const getGameModeInfo = (gameMode) => {
    const modes = {
      normal: { name: 'Classic', icon: '🔤' },
      timed: { name: 'Timed', icon: '⏱️' },
      speed: { name: 'Speed', icon: '⚡' }
    };
    return modes[gameMode] || modes.normal;
  };

  const performance = getPerformanceLevel(results.lettersPerSecond, results.accuracy, results.isFullCompletion);
  const speedGrade = getSpeedGrade(results.lettersPerSecond);
  const modeInfo = getGameModeInfo(results.gameMode);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.round(seconds % 60);
    return mins > 0 ? `${mins}:${secs.toString().padStart(2, '0')}` : `${secs}s`;
  };

  return (
    <div className="az-results fade-in">
      <div className="results-header">
        <div className="performance-badge">
          <span className="performance-icon">{performance.icon}</span>
          <div className="performance-text">
            <h2>A-Z Challenge Complete!</h2>
            <p className={`performance-level ${performance.color}`}>
              {performance.level}
            </p>
          </div>
        </div>
      </div>

      <div className="results-grid">
        {/* Main Stats */}
        <div className="stats-section">
          <h3>Game Performance</h3>
          <div className="main-stats">
            <div className="stat-card primary">
              <div className="stat-icon">📊</div>
              <div className="stat-content">
                <span className="stat-value">{results.lettersCompleted}/26</span>
                <span className="stat-label">Letters Completed</span>
                {results.isFullCompletion && (
                  <span className="completion-badge">🎉 Full Alphabet!</span>
                )}
              </div>
            </div>

            <div className="stat-card success">
              <div className="stat-icon">⚡</div>
              <div className="stat-content">
                <span className="stat-value">{results.lettersPerSecond}</span>
                <span className="stat-label">Letters Per Second</span>
                <span className={`speed-grade ${speedGrade.color}`}>
                  Grade: {speedGrade.grade}
                </span>
              </div>
            </div>

            <div className="stat-card secondary">
              <div className="stat-icon">🎯</div>
              <div className="stat-content">
                <span className="stat-value">{results.accuracy}%</span>
                <span className="stat-label">Accuracy</span>
                <span className="stat-note">
                  {results.errors === 0 ? 'Perfect!' : `${results.errors} errors`}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Detailed Stats */}
        <div className="details-section">
          <h3>Game Details</h3>
          <div className="detail-stats">
            <div className="detail-item">
              <span className="detail-label">⏱️ Total Time</span>
              <span className="detail-value">{formatTime(results.totalTime)}</span>
            </div>
            
            <div className="detail-item">
              <span className="detail-label">🎮 Game Mode</span>
              <span className="detail-value">{modeInfo.icon} {modeInfo.name}</span>
            </div>
            
            <div className="detail-item">
              <span className="detail-label">🔥 Best Streak</span>
              <span className="detail-value">{results.bestStreak} letters</span>
            </div>
            
            <div className="detail-item">
              <span className="detail-label">⚡ Avg Letter Time</span>
              <span className="detail-value">{results.averageLetterTime}ms</span>
            </div>
            
            <div className="detail-item">
              <span className="detail-label">❌ Total Errors</span>
              <span className="detail-value">{results.errors}</span>
            </div>
            
            <div className="detail-item">
              <span className="detail-label">📈 Completion Rate</span>
              <span className="detail-value">{Math.round((results.lettersCompleted / 26) * 100)}%</span>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Visualization */}
      <div className="progress-section">
        <h3>Alphabet Progress</h3>
        <div className="alphabet-progress">
          <div className="alphabet-grid">
            {'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('').map((letter, index) => (
              <div
                key={letter}
                className={`alphabet-letter ${index < results.lettersCompleted ? 'completed' : 'incomplete'}`}
              >
                {letter}
              </div>
            ))}
          </div>
          <div className="progress-stats">
            <span>Completed: {results.lettersCompleted}/26 letters</span>
            <span>Progress: {Math.round((results.lettersCompleted / 26) * 100)}%</span>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="results-actions">
        <button className="action-button secondary" onClick={onRetakeGame}>
          🔄 Play Again
        </button>
        <button className="action-button primary" onClick={onNewGame}>
          🎮 Try Different Mode
        </button>
      </div>

      {/* Tips Section */}
      <div className="tips-section">
        <h3>💡 Tips for Improvement</h3>
        <div className="tips-grid">
          {!results.isFullCompletion && (
            <div className="tip-card">
              <span className="tip-icon">🎯</span>
              <p>Try to complete the full alphabet! Take your time and focus on accuracy.</p>
            </div>
          )}
          {results.lettersPerSecond < 2 && (
            <div className="tip-card">
              <span className="tip-icon">⚡</span>
              <p>Practice typing letters quickly. Try to anticipate the next letter in the sequence.</p>
            </div>
          )}
          {results.errors > 3 && (
            <div className="tip-card">
              <span className="tip-icon">🔍</span>
              <p>Focus on accuracy. Take a moment to locate each letter before typing.</p>
            </div>
          )}
          {results.bestStreak < 10 && (
            <div className="tip-card">
              <span className="tip-icon">🔥</span>
              <p>Build longer streaks by staying focused and typing steadily without mistakes.</p>
            </div>
          )}
          <div className="tip-card">
            <span className="tip-icon">💪</span>
            <p>Regular practice with the A-Z game improves both speed and alphabet familiarity!</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AZGameResults;
