import { useState } from 'react';
import './App.css';
import LevelSelector from './components/LevelSelector';
import TypingTest from './components/TypingTest';
import Results from './components/Results';
import Header from './components/Header';

function App() {
  const [currentScreen, setCurrentScreen] = useState('selector'); // 'selector', 'test', 'results'
  const [selectedDifficulty, setSelectedDifficulty] = useState('easy');
  const [selectedTestType, setSelectedTestType] = useState('basic');
  const [testResults, setTestResults] = useState(null);

  const handleStartTest = (difficulty, testType) => {
    setSelectedDifficulty(difficulty);
    setSelectedTestType(testType);
    setCurrentScreen('test');
  };

  const handleTestComplete = (results) => {
    setTestResults(results);
    setCurrentScreen('results');
  };

  const handleReturnToSelector = () => {
    setCurrentScreen('selector');
    setTestResults(null);
  };

  const handleRetakeTest = () => {
    setCurrentScreen('test');
  };

  return (
    <div className="app">
      <Header />

      <main className="main-content">
        {currentScreen === 'selector' && (
          <LevelSelector onStartTest={handleStartTest} />
        )}

        {currentScreen === 'test' && (
          <TypingTest
            difficulty={selectedDifficulty}
            testType={selectedTestType}
            onComplete={handleTestComplete}
            onBack={handleReturnToSelector}
          />
        )}

        {currentScreen === 'results' && (
          <Results
            results={testResults}
            difficulty={selectedDifficulty}
            testType={selectedTestType}
            onRetakeTest={handleRetakeTest}
            onNewTest={handleReturnToSelector}
          />
        )}
      </main>
    </div>
  );
}

export default App;
