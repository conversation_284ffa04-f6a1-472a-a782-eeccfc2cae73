import { useState } from 'react';
import './App.css';
import LevelSelector from './components/LevelSelector';
import TypingTest from './components/TypingTest';
import Results from './components/Results';
import AZGame from './components/AZGame';
import AZGameResults from './components/AZGameResults';
import RandomGame from './components/RandomGame';
import RandomGameResults from './components/RandomGameResults';
import Header from './components/Header';
import { gameTypes, gameModes, difficultySettings } from './data/characterSets';

function App() {
  const [currentScreen, setCurrentScreen] = useState('selector'); // 'selector', 'test', 'results', 'azgame', 'azresults', 'randomgame', 'randomresults'
  const [selectedDifficulty, setSelectedDifficulty] = useState('easy');
  const [selectedTestType, setSelectedTestType] = useState('basic');
  const [selectedGameMode, setSelectedGameMode] = useState('normal');
  const [selectedRandomGameType, setSelectedRandomGameType] = useState('randomLettersLower');
  const [selectedRandomGameMode, setSelectedRandomGameMode] = useState('practice');
  const [testResults, setTestResults] = useState(null);

  const handleStartTest = (difficulty, testType) => {
    setSelectedDifficulty(difficulty);
    setSelectedTestType(testType);
    setCurrentScreen('test');
  };

  const handleStartAZGame = (gameMode) => {
    // Map testType to gameMode for A-Z game
    const gameModeMap = {
      'basic': 'normal',
      'intermediate': 'timed',
      'advanced': 'speed'
    };
    setSelectedGameMode(gameModeMap[gameMode] || 'normal');
    setCurrentScreen('azgame');
  };

  const handleStartRandomGame = (gameType, gameMode, difficulty) => {
    setSelectedRandomGameType(gameType);
    setSelectedRandomGameMode(gameMode);
    setSelectedDifficulty(difficulty);
    setCurrentScreen('randomgame');
  };

  const handleTestComplete = (results) => {
    setTestResults(results);
    setCurrentScreen('results');
  };

  const handleAZGameComplete = (results) => {
    setTestResults(results);
    setCurrentScreen('azresults');
  };

  const handleRandomGameComplete = (results) => {
    setTestResults(results);
    setCurrentScreen('randomresults');
  };

  const handleReturnToSelector = () => {
    setCurrentScreen('selector');
    setTestResults(null);
  };

  const handleRetakeTest = () => {
    setCurrentScreen('test');
  };

  const handleRetakeAZGame = () => {
    setCurrentScreen('azgame');
  };

  const handleRetakeRandomGame = () => {
    setCurrentScreen('randomgame');
  };

  return (
    <div className="app">
      <Header />

      <main className="main-content">
        {currentScreen === 'selector' && (
          <LevelSelector
            onStartTest={handleStartTest}
            onStartAZGame={handleStartAZGame}
            onStartRandomGame={handleStartRandomGame}
          />
        )}

        {currentScreen === 'test' && (
          <TypingTest
            difficulty={selectedDifficulty}
            testType={selectedTestType}
            onComplete={handleTestComplete}
            onBack={handleReturnToSelector}
          />
        )}

        {currentScreen === 'azgame' && (
          <AZGame
            gameMode={selectedGameMode}
            onComplete={handleAZGameComplete}
            onBack={handleReturnToSelector}
          />
        )}

        {currentScreen === 'randomgame' && (
          <RandomGame
            gameType={gameTypes[selectedRandomGameType]}
            gameMode={gameModes[selectedRandomGameMode]}
            difficulty={difficultySettings[selectedDifficulty]}
            onComplete={handleRandomGameComplete}
            onBack={handleReturnToSelector}
          />
        )}

        {currentScreen === 'results' && (
          <Results
            results={testResults}
            difficulty={selectedDifficulty}
            testType={selectedTestType}
            onRetakeTest={handleRetakeTest}
            onNewTest={handleReturnToSelector}
          />
        )}

        {currentScreen === 'azresults' && (
          <AZGameResults
            results={testResults}
            onRetakeGame={handleRetakeAZGame}
            onNewGame={handleReturnToSelector}
          />
        )}

        {currentScreen === 'randomresults' && (
          <RandomGameResults
            results={testResults}
            onRetakeGame={handleRetakeRandomGame}
            onNewGame={handleReturnToSelector}
          />
        )}
      </main>
    </div>
  );
}

export default App;
